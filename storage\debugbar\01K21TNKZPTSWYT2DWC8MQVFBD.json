{"__meta": {"id": "01K21TNKZPTSWYT2DWC8MQVFBD", "datetime": "2025-08-07 15:34:26", "utime": **********.423067, "method": "GET", "uri": "/employer", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 13, "messages": [{"message": "[15:34:26] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.132566, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.133046, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-dashboard' limit 1\\n-- \",\n    \"Time:\": 18.94\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.188753, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local\\/employer', 'http:\\/\\/recland.local\\/employer?from=registed-success', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36 Edg\\\\\\/*********\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer?from=registed-success\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlU1Mmc5ZVFTWmhVWExCdW9Vb293c3c9PSIsInZhbHVlIjoic1N5K0FKeTZMeTZPOEQ1ZzZvb1g3U1NnektZZWhlMC96OFVpenViaHlxMHFTVEdNOVZhOGQ0eGJNRmwzVVJRWHhyRmNwRm9tUXYwOWlLckVqTFVYMnZHNjAxWmp0dlZSK2VYeGU5b3JPK3orV1YrTklKMG5JM05HQTc5TnBCbWQiLCJtYWMiOiI3MTBlYzkwOTM3YTA4NDZhYmRiNzBmZjA3NjQwYzk5NzhmZDFjNjE5ODk4ZjAxMmZiNDNiMDM0MGI3YjE3MmQ1IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjkzdmNBaVRLODI4bXFFcWdwNkkyWVE9PSIsInZhbHVlIjoiUFpCN3plejV2U2RFOEpSM2J6VjhlbnlBOXBNQTB0TVZLQWVvSzB1aTRMOUlrSkhxMUhmR3J2bklpKzBnQnFRaEVCRTRqb3N0d00wUVhaem1Va0ROSkZzYXZsY09JL0hRZEhQR21EejV2QnVQS0JiOFgxb3B2ZHlMejVUZWlSeHYiLCJtYWMiOiJkNmNiMTM1NDU1MTQwYzViODgwOGFhNzYzZGRlODNjNjcyMWJjOTgyNmVjY2NhNTJiOTI5MDllNTlkMzJlMDY1IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:26', '2025-08-07 15:34:26')\\n-- \",\n    \"Time:\": 0.58\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.350622, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '7490' limit 1\\n-- \",\n    \"Time:\": 0.67\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.359169, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 5.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.369876, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-dashboard' limit 1\\n-- \",\n    \"Time:\": 0.48\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.371882, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'\\n-- \",\n    \"Time:\": 0.51\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.375228, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'\\n-- \",\n    \"Time:\": 2.12\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.380449, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `testimonials` where `type` = '1' and `is_active` = '1'\\n-- \",\n    \"Time:\": 0.53\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.383398, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `categories`\\n-- \",\n    \"Time:\": 0.46\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.407301, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.78\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.411561, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.75\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.41507, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754555665.697331, "end": **********.423096, "duration": 0.7257649898529053, "duration_str": "726ms", "measures": [{"label": "Booting", "start": 1754555665.697331, "relative_start": 0, "end": **********.108177, "relative_end": **********.108177, "duration": 0.****************, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.108186, "relative_start": 0.****************, "end": **********.423099, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "315ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.122581, "relative_start": 0.****************, "end": **********.128104, "relative_end": **********.128104, "duration": 0.005522966384887695, "duration_str": "5.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "61MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "1x frontend.pages.employer.index", "param_count": null, "params": [], "start": **********.394768, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/index.blade.phpfrontend.pages.employer.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Femployer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.employer.index"}, {"name": "1x frontend.pages.employer.employer_v2", "param_count": null, "params": [], "start": **********.397523, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/employer_v2.blade.phpfrontend.pages.employer.employer_v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Femployer%2Femployer_v2.blade.php&line=1", "ajax": false, "filename": "employer_v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.employer.employer_v2"}, {"name": "1x frontend.layouts.v2", "param_count": null, "params": [], "start": **********.404993, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/v2.blade.phpfrontend.layouts.v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fv2.blade.php&line=1", "ajax": false, "filename": "v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.v2"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": **********.405885, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.v2.home_header", "param_count": null, "params": [], "start": **********.408838, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/home_header.blade.phpfrontend.inc_layouts.v2.home_header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhome_header.blade.php&line=1", "ajax": false, "filename": "home_header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.home_header"}, {"name": "1x frontend.inc_layouts.v2.hh_footer", "param_count": null, "params": [], "start": **********.416429, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/hh_footer.blade.phpfrontend.inc_layouts.v2.hh_footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhh_footer.blade.php&line=1", "ajax": false, "filename": "hh_footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.hh_footer"}, {"name": "1x frontend.partials.bug_report_modal", "param_count": null, "params": [], "start": **********.417406, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/partials/bug_report_modal.blade.phpfrontend.partials.bug_report_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fbug_report_modal.blade.php&line=1", "ajax": false, "filename": "bug_report_modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.partials.bug_report_modal"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.418732, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}, {"name": "1x frontend.inc_layouts.v2.contactus", "param_count": null, "params": [], "start": **********.419225, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/contactus.blade.phpfrontend.inc_layouts.v2.contactus", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fcontactus.blade.php&line=1", "ajax": false, "filename": "contactus.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.contactus"}]}, "route": {"uri": "GET employer", "middleware": "web, localization, visit-website, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=137\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "as": "employer-dashboard", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=137\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:137-160</a>"}, "queries": {"count": 11, "nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03125, "accumulated_duration_str": "31.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-dashboard' limit 1", "type": "query", "params": [], "bindings": ["employer-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.170114, "duration": 0.018940000000000002, "duration_str": "18.94ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "66c7743e75d9ecedbdaa32761a6c1bad45af73bcbd94805400ab00c7698da8a4"}, "start_percent": 0, "width_percent": 60.608}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer', 'http://recland.local/employer?from=registed-success', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer?from=registed-success\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlU1Mmc5ZVFTWmhVWExCdW9Vb293c3c9PSIsInZhbHVlIjoic1N5K0FKeTZMeTZPOEQ1ZzZvb1g3U1NnektZZWhlMC96OFVpenViaHlxMHFTVEdNOVZhOGQ0eGJNRmwzVVJRWHhyRmNwRm9tUXYwOWlLckVqTFVYMnZHNjAxWmp0dlZSK2VYeGU5b3JPK3orV1YrTklKMG5JM05HQTc5TnBCbWQiLCJtYWMiOiI3MTBlYzkwOTM3YTA4NDZhYmRiNzBmZjA3NjQwYzk5NzhmZDFjNjE5ODk4ZjAxMmZiNDNiMDM0MGI3YjE3MmQ1IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjkzdmNBaVRLODI4bXFFcWdwNkkyWVE9PSIsInZhbHVlIjoiUFpCN3plejV2U2RFOEpSM2J6VjhlbnlBOXBNQTB0TVZLQWVvSzB1aTRMOUlrSkhxMUhmR3J2bklpKzBnQnFRaEVCRTRqb3N0d00wUVhaem1Va0ROSkZzYXZsY09JL0hRZEhQR21EejV2QnVQS0JiOFgxb3B2ZHlMejVUZWlSeHYiLCJtYWMiOiJkNmNiMTM1NDU1MTQwYzViODgwOGFhNzYzZGRlODNjNjcyMWJjOTgyNmVjY2NhNTJiOTI5MDllNTlkMzJlMDY1IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:26', '2025-08-07 15:34:26')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local/employer", "http://recland.local/employer?from=registed-success", "[\"en-us\",\"en\",\"vi\",\"nl\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer?from=registed-success\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlU1Mmc5ZVFTWmhVWExCdW9Vb293c3c9PSIsInZhbHVlIjoic1N5K0FKeTZMeTZPOEQ1ZzZvb1g3U1NnektZZWhlMC96OFVpenViaHlxMHFTVEdNOVZhOGQ0eGJNRmwzVVJRWHhyRmNwRm9tUXYwOWlLckVqTFVYMnZHNjAxWmp0dlZSK2VYeGU5b3JPK3orV1YrTklKMG5JM05HQTc5TnBCbWQiLCJtYWMiOiI3MTBlYzkwOTM3YTA4NDZhYmRiNzBmZjA3NjQwYzk5NzhmZDFjNjE5ODk4ZjAxMmZiNDNiMDM0MGI3YjE3MmQ1IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjkzdmNBaVRLODI4bXFFcWdwNkkyWVE9PSIsInZhbHVlIjoiUFpCN3plejV2U2RFOEpSM2J6VjhlbnlBOXBNQTB0TVZLQWVvSzB1aTRMOUlrSkhxMUhmR3J2bklpKzBnQnFRaEVCRTRqb3N0d00wUVhaem1Va0ROSkZzYXZsY09JL0hRZEhQR21EejV2QnVQS0JiOFgxb3B2ZHlMejVUZWlSeHYiLCJtYWMiOiJkNmNiMTM1NDU1MTQwYzViODgwOGFhNzYzZGRlODNjNjcyMWJjOTgyNmVjY2NhNTJiOTI5MDllNTlkMzJlMDY1IiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Edge", "127.0.0.1", null, null, "2025-08-07 15:34:26", "2025-08-07 15:34:26"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.350123, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 60.608, "width_percent": 1.856}, {"sql": "select * from `users` where `id` = 7490 limit 1", "type": "query", "params": [], "bindings": [7490], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 19, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}], "start": **********.35857, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "46fb2a7e2da69ce8dd6b76eee0f083f3fd38c5f3e291e80b6e2a5dd1df6426fd"}, "start_percent": 62.464, "width_percent": 2.144}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7490 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7490], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.364517, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:125", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=125", "ajax": false, "filename": "EmployerController.php", "line": "125"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "2acb24b13514507d20205de68fbb9c1fa196748160210213f26fc2003be4b8af"}, "start_percent": 64.608, "width_percent": 17.376}, {"sql": "select * from `seos` where `key` = 'employer-dashboard' limit 1", "type": "query", "params": [], "bindings": ["employer-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 140}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3714712, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "66c7743e75d9ecedbdaa32761a6c1bad45af73bcbd94805400ab00c7698da8a4"}, "start_percent": 81.984, "width_percent": 1.536}, {"sql": "select * from `banners` where `is_active` = 1 and `position` = 'home-ntd-banner' and `type` = 1", "type": "query", "params": [], "bindings": [1, "home-ntd-banner", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/BannerRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php", "line": 62}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/BannerService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php", "line": 21}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 142}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.374787, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BannerRepository.php:62", "source": {"index": 14, "namespace": null, "name": "app/Repositories/BannerRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBannerRepository.php&line=62", "ajax": false, "filename": "BannerRepository.php", "line": "62"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `banners` where `is_active` = ? and `position` = ? and `type` = ?", "hash": "3059d80fc8b8a60aefda83a0c5b766c9f56e35c588c658f4b216f214abf33f69"}, "start_percent": 83.52, "width_percent": 1.632}, {"sql": "select `id`, `name`, `slug`, `logo` from `companies` where `home` = 1 and `is_active` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\CompanyRepository.php", "line": 83}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\CompanyService.php", "line": 23}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 153}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3784, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:83", "source": {"index": 14, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\CompanyRepository.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FCompanyRepository.php&line=83", "ajax": false, "filename": "CompanyRepository.php", "line": "83"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `id`, `name`, `slug`, `logo` from `companies` where `home` = ? and `is_active` = ?", "hash": "492890de082540e1fb8fb61777a7859a19f08d599af94a993907cdeac1ca6bb5"}, "start_percent": 85.152, "width_percent": 6.784}, {"sql": "select * from `testimonials` where `type` = 1 and `is_active` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/TestimonialRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\TestimonialRepository.php", "line": 54}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/TestimonialService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\TestimonialService.php", "line": 20}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 155}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.382938, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "TestimonialRepository.php:54", "source": {"index": 14, "namespace": null, "name": "app/Repositories/TestimonialRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\TestimonialRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FTestimonialRepository.php&line=54", "ajax": false, "filename": "TestimonialRepository.php", "line": "54"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `testimonials` where `type` = ? and `is_active` = ?", "hash": "85fe956c1e6a00482970b8c6831cc24306d6a878a633d923d1d249a76caa5018"}, "start_percent": 91.936, "width_percent": 1.696}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}, {"index": 21, "namespace": "view", "name": "frontend.layouts.v2", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/v2.blade.php", "line": 46}], "start": **********.4069102, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:55", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=55", "ajax": false, "filename": "AppServiceProvider.php", "line": "55"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `categories`", "hash": "7e13c5da69976ceecb727777c5a91eea3e71bf2b1b50247c4cb202cd9a066582"}, "start_percent": 93.632, "width_percent": 1.472}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.inc_layouts.v2.home_header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/home_header.blade.php", "line": 149}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.410852, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.v2.home_header:149", "source": {"index": 19, "namespace": "view", "name": "frontend.inc_layouts.v2.home_header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/home_header.blade.php", "line": 149}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhome_header.blade.php&line=149", "ajax": false, "filename": "home_header.blade.php", "line": "149"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 95.104, "width_percent": 2.496}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.inc_layouts.v2.home_header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/home_header.blade.php", "line": 377}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.4143898, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.v2.home_header:377", "source": {"index": 19, "namespace": "view", "name": "frontend.inc_layouts.v2.home_header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/home_header.blade.php", "line": 377}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhome_header.blade.php&line=377", "ajax": false, "filename": "home_header.blade.php", "line": "377"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 97.6, "width_percent": 2.4}]}, "models": {"data": {"App\\Models\\Company": {"retrieved": 86, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Category": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Testimonial": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FTestimonial.php&line=1", "ajax": false, "filename": "Testimonial.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "App\\Models\\EmployerType": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmployerType.php&line=1", "ajax": false, "filename": "EmployerType.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "App\\Models\\Banner": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FBanner.php&line=1", "ajax": false, "filename": "Banner.php", "line": "?"}}}, "count": 102, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 101, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7490"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/employer", "action_name": "employer-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@index", "uri": "GET employer", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=137\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=137\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:137-160</a>", "middleware": "web, localization, visit-website", "duration": "728ms", "peak_memory": "64MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-782232241 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-782232241\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2057731226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2057731226\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1165129077 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">http://recland.local/employer?from=registed-success</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlU1Mmc5ZVFTWmhVWExCdW9Vb293c3c9PSIsInZhbHVlIjoic1N5K0FKeTZMeTZPOEQ1ZzZvb1g3U1NnektZZWhlMC96OFVpenViaHlxMHFTVEdNOVZhOGQ0eGJNRmwzVVJRWHhyRmNwRm9tUXYwOWlLckVqTFVYMnZHNjAxWmp0dlZSK2VYeGU5b3JPK3orV1YrTklKMG5JM05HQTc5TnBCbWQiLCJtYWMiOiI3MTBlYzkwOTM3YTA4NDZhYmRiNzBmZjA3NjQwYzk5NzhmZDFjNjE5ODk4ZjAxMmZiNDNiMDM0MGI3YjE3MmQ1IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjkzdmNBaVRLODI4bXFFcWdwNkkyWVE9PSIsInZhbHVlIjoiUFpCN3plejV2U2RFOEpSM2J6VjhlbnlBOXBNQTB0TVZLQWVvSzB1aTRMOUlrSkhxMUhmR3J2bklpKzBnQnFRaEVCRTRqb3N0d00wUVhaem1Va0ROSkZzYXZsY09JL0hRZEhQR21EejV2QnVQS0JiOFgxb3B2ZHlMejVUZWlSeHYiLCJtYWMiOiJkNmNiMTM1NDU1MTQwYzViODgwOGFhNzYzZGRlODNjNjcyMWJjOTgyNmVjY2NhNTJiOTI5MDllNTlkMzJlMDY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165129077\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1486137042 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pueeXwEzr1acgKc7V4faWcjl58Z97qy6xnixJ1jx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486137042\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1720150038 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 08:34:26 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720150038\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-18022592 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"91 characters\">http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7490</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18022592\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/employer", "action_name": "employer-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@index"}, "badge": null}}
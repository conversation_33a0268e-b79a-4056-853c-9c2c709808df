<?php

namespace App\Http\Controllers\Frontend;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\DepositRequest;
use App\Http\Requests\Frontend\JobRequest;
use App\Http\Requests\Frontend\RecChangePasswordRequest;
use App\Http\Requests\Frontend\RegisterInviteRequest;
use App\Models\JobTop;
use App\Models\Skill;
use App\Models\SkillMain;
use App\Models\SubmitCv;
use App\Models\User;
use App\Repositories\WalletRepository;
use App\Services\Admin\SubmitCvService as SubmitCvServiceAdmin;
use App\Services\Frontend\SubmitCvService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\WareHouseCvSellingBuyOnboardService;
use App\Services\Frontend\JobService;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\SettingService;
use App\Services\Frontend\BannerService;
use App\Services\Frontend\CompanyService;
use App\Services\Frontend\SubmitCvBookService;
use App\Services\Frontend\SubmitCvOnboardService;
use App\Services\Frontend\TestimonialService;
use App\Services\Frontend\UserService;
use App\Services\Frontend\WareHouseCvSellingBuyBookService;
use App\Services\Frontend\WareHouseCvSellingBuyDiscussService;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use App\Services\Frontend\WareHouseSubmitCvService;
use App\Services\Frontend\SubmitCvDiscussService;
use App\Services\Frontend\ZalopayTransactionService;
use App\Services\Frontend\WalletTransactionService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\Frontend\WarehouseCvSellingHistoryBuyService;
use App\Services\Frontend\WareHouseCvSellingService;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class EmployerController extends Controller
{
    protected $bannerService;
    protected $testimonialService;
    protected $companyService;
    protected $settingService;
    protected $seoService;
    protected $jobService;
    protected $wareHouseSubmitCvService;
    protected $userService;
    protected $routeName;
    protected $wareHouseCvSellingBuyService;
    protected $submitCvService;
    protected $submitCvServiceAdmin;
    protected $wareHouseCvSellingBuyDiscussService;
    protected $submitCvBookService;
    protected $warehouseCvSellingHistoryBuyService;
    protected $warehouseCvSellingBuyOnboardService;
    protected $submitCvOnboardService;
    // protected $submitCv;
    protected $submitCvDiscussService;
    protected $wareHouseCvSellingBuyBookService;
    protected $walletTransactionService;
    protected $walletRepo;

    protected $submitCv;

    public function __construct(
        BannerService            $bannerService,
        TestimonialService       $testimonialService,
        CompanyService           $companyService,
        SettingService           $settingService,
        SeoService               $seoService,
        JobService               $jobService,
        WareHouseSubmitCvService $wareHouseSubmitCvService,
        SubmitCvService $submitCvService,
        SubmitCvServiceAdmin $submitCvServiceAdmin,
        UserService              $userService,
        SubmitCvDiscussService              $submitCvDiscussService,
        WareHouseCvSellingBuyService $wareHouseCvSellingBuyService,
        WareHouseCvSellingBuyDiscussService $wareHouseCvSellingBuyDiscussService,
        SubmitCvBookService $submitCvBookService,
        WarehouseCvSellingHistoryBuyService $warehouseCvSellingHistoryBuyService,
        SubmitCvOnboardService $submitCvOnboardService,
        WareHouseCvSellingBuyBookService $wareHouseCvSellingBuyBookService,
        // SubmitCv $submitCv,
        WareHouseCvSellingBuyOnboardService $warehouseCvSellingBuyOnboardService,
        SubmitCvService $submitCv,
        WalletRepository $walletRepo,
        WalletTransactionService $walletTransactionService
    ) {
        $this->bannerService = $bannerService;
        $this->testimonialService = $testimonialService;
        $this->companyService = $companyService;
        $this->settingService = $settingService;
        $this->seoService = $seoService;
        $this->jobService = $jobService;
        $this->wareHouseSubmitCvService = $wareHouseSubmitCvService;
        $this->submitCvService = $submitCvService;
        $this->submitCvServiceAdmin = $submitCvServiceAdmin;
        $this->userService = $userService;
        // $this->submitCv = $submitCv;
        $this->warehouseCvSellingBuyOnboardService = $warehouseCvSellingBuyOnboardService;
        $this->submitCvDiscussService = $submitCvDiscussService;
        $this->wareHouseCvSellingBuyService = $wareHouseCvSellingBuyService;
        $this->wareHouseCvSellingBuyDiscussService = $wareHouseCvSellingBuyDiscussService;
        $this->submitCvBookService = $submitCvBookService;
        $this->warehouseCvSellingHistoryBuyService = $warehouseCvSellingHistoryBuyService;
        $this->submitCvOnboardService = $submitCvOnboardService;
        $this->wareHouseCvSellingBuyBookService = $wareHouseCvSellingBuyBookService;
        $this->warehouseCvSellingBuyOnboardService = $warehouseCvSellingBuyOnboardService;
        $this->submitCv = $submitCv;
        $this->walletRepo = $walletRepo;
        $this->walletTransactionService = $walletTransactionService;
        $this->routeName = \Route::currentRouteName();
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
        $this->middleware(function ($request, $next) {
            $user = auth('client')->user();
            $wallet = $user['wallet'] ?? '';
            $employerIsActive = $user && $user->company_id ? 1 : 0;
            \View::share('employerIsActive', $employerIsActive);
            \View::share('wallet_amoumt', $wallet ? $wallet->amount : 0);
            return $next($request);
        });
    }

    /**
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     * Trang chủ NTD
     */
    public function index(Request $request)
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
        //banner NTD
        $listBanners = $this->bannerService->getListByType(config('constant.role_frontend_revest.ntd'), strtolower(config('constant.position_banner.home-ntd-banner')));
        //logo công ty

        $arrBanner = [];
        foreach ($listBanners as $item) {
            if ($lang == config('constant.language.vi')) {
                array_push($arrBanner, $item->image_url_vn);
            } else {
                array_push($arrBanner, $item->image_url_en);
            }
        }
        $listCompanies = $this->companyService->getListByHome();
        //testimonations NTD
        $listTestimonations = $this->testimonialService->getListByType(config('constant.role_frontend_revest.ntd'));

        return view('frontend.pages.employer.index', compact(
            'listTestimonations',
        ));
    }

    public function listJob(Request $request)
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
        $user = auth('client')->user();
        $userId = $user->id;
        // if (isset($user->userEmployerType->type) && $user->userEmployerType->type == 'manager') $userId = null; // thay đổi là ntd nào xem được của ng đó thôi
        $params = $request->all();
        $params['company_id'] = $user->company_id;
        $arrJob = $this->jobService->getJobEmployer($params, $userId);

        $totalJobActive = $this->jobService->totalJob([
            'is_active' => config('constant.active'),
            'employer_id' => $userId,
        ]);

        $totalJobInactive = $this->jobService->totalJob([
            'is_active' => config('constant.inActive'),
            'employer_id' => $userId,
        ]);

        $lang = app()->getLocale();
        $str = 'employer_job';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        $editjob = $createjob = $employersubmitcvbyjob = 0;
        if ($user->userEmployerType->type == 'manager') {
            $editjob = $createjob = $employersubmitcvbyjob = 1;
        } else {
            $roles = json_decode($user->userEmployerType->employeeRole->permission, true);

            if (in_array('employer-edit', $roles)) {
                $editjob = 1;
            }

            if (in_array('employer-create', $roles)) {
                $createjob = 1;
            }

            if (in_array('employer-submitcv-by-job', $roles)) {
                $employersubmitcvbyjob = 1;
            }
        }
        // $userDataWithIsInvalid = $this->userService->getStatusEmployerIsValid();
        return view('frontend.pages.employer.list-job', compact(
            'editjob',
            'createjob',
            'employersubmitcvbyjob',
            'arrJob',
            'totalJobActive',
            'totalJobInactive',
            'arrLang'
        ));
    }

    public function dashboard(Request $request)
    {
        $user = auth('client')->user();
        $userId = $user->id;

        $totalJobActive = $this->jobService->totalJob([
            'is_active' => config('constant.active'),
            'employer_id' => $user->id,
        ]);

        $countJobOnboardByUserId = $this->wareHouseSubmitCvService->countSubmitcvByCondition(['status' => config('constant.submit_cvs_status_value.onboarded'), 'employer_id' => $user->id]);
        $countTotalSubmitCvSkipDraftByUserId = $this->wareHouseSubmitCvService->countSubmitcvByCondition(['not_in_status' => config('constant.submit_cvs_status_value.draft'), 'employer_id' => $user->id]);

        $now = Carbon::now();
        $end = $now->lastOfMonth()->format('d');
        $daysOfMonth = [];
        for ($i = 1; $i <= (int)$end; $i++) {
            $daysOfMonth[] = $i;
        }
        $statistical = $this->wareHouseSubmitCvService->statisticalEmployerFrontend(auth('client')->id());


        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
        $str = 'employer_dashboard';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        $countTotalCV = $this->submitCvServiceAdmin->totalCv([
            'is_active' => config('constant.active'),
            'employer_id' => $user->id,
        ]);
        $pointWallet = $this->walletRepo->getPointWallet(auth('client')->user()->id);

        $dataDeposit = $this->userService->getDeposit($request->all());
        $statical = $this->userService->statisticalDeposit();

        $dataEmployerGetHistory = $this->warehouseCvSellingHistoryBuyService->employerGetHistory($request->all());
        $spentThisMonth = $this->warehouseCvSellingHistoryBuyService->spentThisMonth();
        $spentThisYear = $this->warehouseCvSellingHistoryBuyService->spentThisYear();

        // $userDataWithIsInvalid = $this->userService->getStatusEmployerIsValid();

        //        dd($userDataWithIsInvalid);
        return view(
            'frontend.pages.employer.dashboard',
            compact(
                'totalJobActive',
                'countJobOnboardByUserId',
                'countTotalSubmitCvSkipDraftByUserId',
                'statistical',
                'daysOfMonth',
                'now',
                'countTotalCV',
                'pointWallet',
                'dataDeposit',
                'statical',
                'dataEmployerGetHistory',
                'spentThisMonth',
                'spentThisYear'
            )
        );
    }

    public function createJob()
    {

        $lang = app()->getLocale();
        $back_to = request()->get('back_to');

        if ($back_to) {
            session()->put('back_to', $back_to);
        }
        $user = auth('client')->user();
        if (!$user->company || empty($user->company->address_value)) {
            return redirect()->route('employer-company-profile', ['msg' => 'update-company-info-required']);
        }


        $bonusTypes = config('job.bonus_type.' . $lang);
        $this->seoService->getConfig($this->routeName, $lang);
        $this->generateParams();
        return view('frontend.pages.employer.create', compact('bonusTypes'));
    }

    private function generateParams()
    {
        $lang = app()->getLocale();
        $str = 'employer_job';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        $cities = Common::getCities();
        $career = config('job.career.' . $lang);
        $rank = config('job.rank.' . $lang);
        $type = config('job.type.' . $lang);
        $bonusType = config('job.bonus_type.' . $lang);
        $currency = config('constant.currency');
        $isActive = config('job.is_active.' . $lang);
        $status = config('job.status.' . $lang);
        $skills = $this->jobService->getSkill();
        $yearOfExperience = config('constant.sonamkinhnghiem');
        $candidateStatus = config('constant.thoigiandilamdukien');

        view()->share([
            'cities' => $cities,
            'career' => $career,
            'rank' => $rank,
            'type' => $type,
            'bonusType' => $bonusType,
            'currency' => $currency,
            'arrLang' => $arrLang,
            'skills' => $skills,
            'isActive' => $isActive,
            'status' => $status,
            'yearOfExperience' => $yearOfExperience,
            'candidateStatus' => $candidateStatus,
        ]);
    }

    public function storeJob(JobRequest $request)
    {
        try {
            DB::beginTransaction();
            $this->jobService->createJob($request->all());
            DB::commit();
            Toast::success(__('message.edit_success'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error employer store job: ', [
                'content: ' => $e->getMessage()
            ]);
        }
        $back_to = Session::get('back_to');
        if (!empty($back_to)) {
            return redirect($back_to);
        } else {
            return redirect()->route('employer-job');
        }
    }

    public function companyProfile()
    {
        $lang = app()->getLocale();

        $str = 'employer_company_profile';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        $this->seoService->getConfig($this->routeName, $lang);
        $user = auth('client')->user()->load('userEmployerType.employeeRole');
        $flg['thongtin_congty'] = 0;
        $flg['thongtin_ntd'] = 0;
        $flg['doimatkhau'] = 0;
        if ($user->userEmployerType->type == 'manager') {
            $flg['thongtin_congty'] = 1;
            $flg['thongtin_ntd'] = 1;
            $flg['doimatkhau'] = 1;
        } else {
            $roles = json_decode($user->userEmployerType->employeeRole->permission, true);

            if (in_array('employer-change-info-company', $roles)) {
                $flg['thongtin_congty'] = 1;
            }

            if (in_array('employer-change-user', $roles)) {
                $flg['thongtin_ntd'] = 1;
            }

            if (in_array('employer-change-password', $roles)) {
                $flg['doimatkhau'] = 1;
            }
        }

        $company = $user->company;
        $cities = Common::getCities();
        $career = config('job.career.' . $lang);
        // $userDataWithIsInvalid = $this->userService->getStatusEmployerIsValid();

        return view('frontend.pages.employer.company_profile', compact('company', 'cities', 'user', 'arrLang', 'career', 'flg'));
    }

    public function changePassword(RecChangePasswordRequest $request)
    {
        $this->userService->changePasswordById(auth('client')->user()->id, $request->password);
        Toast::success(__('message.change_password_success'));
        return back();
    }

    public function changeUser(Request $request)
    {
        $this->userService->changeUserById(auth('client')->user()->id, $request->all());
        Toast::success(__('message.change_user_success'));
        return back();
    }

    /**
     * @param $slug
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     * Danh sach ứng tuyển vào 1 job của NTD
     */
    public function submitcvByJob($slug, Request $request)
    {
        $user = auth('client')->user();
        //check job

        if ($user->userEmployerType->type == 'manager') {
            $job = $this->jobService->findBySlugAllExpire($slug, null);
        } else {
            $job = $this->jobService->findBySlugAllExpire($slug, $user->id);
        }

        if (!$job || $user->company_id != $job->company_id) {
            Toast::warning(__('frontend/collaborator/message.data_not_exit'));
            return redirect()->route('employer-job');
        }

        if ($job) {
            $user = $user->load('userEmployerType.employeeRole');
            $capnhattrangthai = 0;
            if ($user->userEmployerType->type == 'manager') {
                $capnhattrangthai = 1;
            } else {
                $roles = json_decode($user->userEmployerType->employeeRole->permission, true);

                if (in_array('employer-submitcv-status-change', $roles)) {
                    $capnhattrangthai = 1;
                }
            }

            $lang = app()->getLocale();
            $str = 'employer_submitcv';
            $arrLang = $this->settingService->getAllByKey($str, $lang);
            //get list warehouse_cvs
            $params = $request->all();
            $params['user_id'] = $user->id;
            $params['job_id'] = $job->id;
            $params['authorize'] = 1;
            $arrSubmitCv = $this->wareHouseSubmitCvService->getListSubmitCvByJobSearch($params);
            $submitCvsStatus = config('constant.submit_cvs_status');
            $bonusType = config('constant.bonus_type');

            return view('frontend.pages.employer.submitcv_by_job', compact('capnhattrangthai', 'arrLang', 'job', 'arrSubmitCv', 'submitCvsStatus', 'bonusType'));
        } else {
            Toast::warning(__('frontend/collaborator/message.data_not_exit'));

            return redirect()->route('employer-job');
        }
    }

    /**
     * @param $slug
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     * Danh sach ứng tuyển của tất cả job NTD
     */
    public function submitcvByEmployer(Request $request, $slug = null)
    {
        $user = auth('client')->user()->load('userEmployerType.employeeRole');

        //check job
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
        $str = 'employer_submitcv';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        //get list warehouse_cvs
        $params = $request->all();

        $capnhattrangthai = 0;
        if ($user->userEmployerType->type == 'manager') {
            $capnhattrangthai = 1;
            // $params['user_id'] = null;
            $params['user_id'] = $user->id; // thay đổi là ntd nào xem được của ng đó thôi
            $conditionDt['employer_id'] = $user->id;
            $condition['employer_id'] = $user->id;
            // $params['company_id'] = $user->company_id;
            // $condition['company_id'] = $user->company_id;
            // $conditionDt['company_id'] = $user->company_id;
        } else {
            $roles = json_decode($user->userEmployerType->employeeRole->permission, true);

            if (in_array('employer-submitcv-status-change', $roles)) {
                $capnhattrangthai = 1;
            }
            $params['user_id'] = $user->id;
            $conditionDt['employer_id'] = $user->id;
            $condition['employer_id'] = $user->id;
        }
        $job = null;
        $url = route('employer-submitcv');
        if ($slug) {
            $params['job_slug'] = $slug;
            $job = $this->jobService->findBySlugAllExpire($slug, $user->id);
            $url = route('employer-submitcv-by-job', ['slug' => $slug]);
        }

        $params['authorize'] = 1;
        $arrSubmitCv = $this->wareHouseSubmitCvService->getListSubmitByNtd($params);
        // dd($arrSubmitCv);

        //Ho so ung vien Đã tuyen đươc: onboarded, pass-interview
        $statusDt = [
            config('constant.status_recruitment_revert.PassInterview'),
            config('constant.status_recruitment_revert.Offering'),
            config('constant.status_recruitment_revert.Waitingonboard'),
            config('constant.status_recruitment_revert.Trialwork'),
            config('constant.status_recruitment_revert.SuccessRecruitment'),
            config('constant.status_recruitment_revert.BuyCVdatasuccessfull'),
        ];

        $conditionDt['in_status'] = $statusDt;
        $countJobDt = $this->wareHouseSubmitCvService->countSubmitcvByCondition($conditionDt);
        //Ho so ung vien dang cho: Pending
        $condition['status'] = config('constant.status_recruitment_revert.WaitingPayment');
        $countJobPending = $this->wareHouseSubmitCvService->countSubmitcvByCondition($condition);
        //Ho so ung vien da duyet: accepted
        $condition['status'] = null;
        $condition['not_in_status'] = [
            config('constant.status_recruitment_revert.Waitingcandidateconfirm'),
            config('constant.status_recruitment_revert.CandidateCancelApply'),
            config('constant.status_recruitment_revert.WaitingPayment'),
        ];
        $countJobAccepted = $this->wareHouseSubmitCvService->countSubmitcvByCondition($condition);
        //Ho so ung vien bi loai: reject
        unset($condition['not_in_status']);
        $condition['status'] = config('constant.status_recruitment_revert.RecruiterRejectCV');
        $countJobReject = $this->wareHouseSubmitCvService->countSubmitcvByCondition($condition);

        $submitCvsStatus = config('constant.status_recruitment.vi');
        $bonusType = config('constant.bonus_type');

        //bộ lọc
        $this->generateParams();
        // $userDataWithIsInvalid = $this->userService->getStatusEmployerIsValid();
        return view('frontend.pages.employer.submitcv', compact(
            'capnhattrangthai',
            'arrLang',
            'arrSubmitCv',
            'countJobDt',
            'countJobPending',
            'countJobAccepted',
            'countJobReject',
            'submitCvsStatus',
            'bonusType',
            'url',
            'job'
        ));
    }

    public function editJob($id)
    {
        $lang = app()->getLocale();
        $user = auth('client')->user();
        if ($user->userEmployerType->type == 'manager') {
            $job = $this->jobService->detailService($id, null);
        } else {
            $job = $this->jobService->detailService($id, $user);
        }

        if (!$job || $user->company_id != $job->company_id) {
            Toast::warning(__('frontend/collaborator/message.data_not_exit'));
            return redirect()->route('employer-job');
        }
        $bonusTypes = config('job.bonus_type.' . $lang);

        $this->generateParams();

        $isIt = in_array($job->career, config('job.it_career'));
        if ($isIt) {
            $skills = SkillMain::all()->pluck('name_' . $lang, 'id');
            $skill = SkillMain::where('id', $job->skill_id)->first();
        } else {
            $skills = JobTop::all()->pluck('name_' . $lang, 'id');
            $skill = JobTop::where('id', $job->skill_id)->first();
        }
        $wareHouseCvSellingService = resolve(WareHouseCvSellingService::class);
        $levels = $wareHouseCvSellingService->getLevelByCareer(['career_id' => $job->career, 'skill_id' => $job->skill_id]);
        if (count($levels)) {
            $levels = $levels->pluck('name_' . $lang, 'id');
        } else {
            $levels = [];
        }
        return view('frontend.pages.employer.edit', compact('job', 'skill', 'isIt', 'bonusTypes', 'skills', 'levels'));
    }

    /**
     * @param $id
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     * Chi tiết giới thiệu ứng viên
     */
    public function detailSubmitCv($id)
    {
        $user = auth('client')->user();

        if ($user->userEmployerType->type == 'manager') {
            $userId = null;
        } else {
            $userId = $user->id;
        }

        $submitCv = $this->wareHouseSubmitCvService->getDetailSubmitCvByEmployer($id, $userId);
        $this->generateParamSubmitcvs();

        $start = $this->checkShowData(optional($submitCv->job)->bonus_type, $submitCv->status);

        if ($submitCv) {
            $lang = app()->getLocale();
            $str = 'employer_submitcv';
            $arrLang = $this->settingService->getAllByKey($str, $lang);
            $career = implode(', ', array_intersect_key(config('job.career.' . app()->getLocale()), array_flip(explode(',', $submitCv->career))));
            $view = view('frontend.pages.employer.modal.detail_submitcv', compact('submitCv', 'arrLang', 'start', 'career'));
            $view = $view->render();
            return $view;
        } else {
            Toast::warning(__('frontend/collaborator/message.submit_cv_not_exit'));

            return redirect()->route('rec-submitcv');
        }
    }

    /**
     * @param $id
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     * CẠp nhat trang thái
     */
    public function detailStatusSubmitCv($id)
    {
        $user = auth('client')->user();
        if ($user->userEmployerType->type == 'manager') {
            $userId = null;
        } else {
            $userId = $user->id;
        }

        $submitCv = $this->wareHouseSubmitCvService->getDetailSubmitCvByEmployer($id, $userId);
        $submitCvsStatus = config('constant.submit_cvs_status');
        unset($submitCvsStatus[7]);
        unset($submitCvsStatus[9]);
        unset($submitCvsStatus[10]);
        unset($submitCvsStatus[11]);
        unset($submitCvsStatus[12]);

        if ($submitCv->job->bonus_type == 'cv' || $submitCv->job->bonus_type == 'interview') {
            unset($submitCvsStatus[5]);
            unset($submitCvsStatus[8]);
        }
        if (isset($submitCv['warehouse_cv'])) {
            if (isset($submitCv['warehouse_cv']['url_cv_public'])) unset($submitCv['warehouse_cv']['url_cv_public']);
            if (isset($submitCv['warehouse_cv']['name_cv_public'])) unset($submitCv['warehouse_cv']['name_cv_public']);
        }
        if (isset($submitCv['submit_cv_meta'])) {
            if (isset($submitCv['submit_cv_meta']['cv_public'])) unset($submitCv['submit_cv_meta']['cv_public']);
            if (isset($submitCv['submit_cv_meta']['url_cv_public'])) unset($submitCv['submit_cv_meta']['url_cv_public']);
            if (isset($submitCv['submit_cv_meta']['name_cv_public'])) unset($submitCv['submit_cv_meta']['name_cv_public']);
        }
        if (isset($submitCv['code'])) {
            unset($submitCv['code']);
        }

        $submitCv['status_show'] = $submitCvsStatus;
        $submitCv['expire_date_change_status'] = $this->wareHouseSubmitCvService->getExpiredDateByStatus($submitCv->date_change_status, $submitCv->status);
        //        dd($submitCv);

        return $submitCv;
    }

    /**
     * @param $id
     *
     * @return \Illuminate\Http\RedirectResponse
     * CẠp nhat trang thái
     */
    public function changeStatusSubmitCv(Request $request)
    {
        $user = auth('client')->user();
        $data = $request->all();

        if ($user->userEmployerType->type == 'manager') {
            $data['employer_id'] = 0;
        } else {
            $data['employer_id'] = $user->id;
        }

        $response = $this->wareHouseSubmitCvService->changeStatusSubmitCvByEmployer($data);
        if ($response['error'] == '' && $response['error_assessment'] == '') Toast::success(__('message.edit_success'));

        return $response;
    }

    private function generateParamSubmitcvs()
    {
        $lang = app()->getLocale();

        $submitCvsStatus = config('constant.submit_cvs_status');
        $submit_cvs_status_value = config('constant.submit_cvs_status_value');
        $currency = config('constant.currency');

        $str = 'rec_submitcv';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        view()->share([
            'submitCvsStatus' => $submitCvsStatus,
            'submit_cvs_status_value' => $submit_cvs_status_value,
            'currency' => $currency,
            'arrLang' => $arrLang,
        ]);
    }

    public function updateJob($id, JobRequest $request)
    {
        $this->jobService->updateJob($id, $request->all());
        Toast::success(__('message.edit_success'));
        return redirect()->route('employer-job');
    }

    public function users(Request $request)
    {
        $lang = app()->getLocale();
        $str = 'employer_user';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        $companyId = auth('client')->user()->company_id;
        $data = $this->userService->getUserCompany($companyId, $request->all());
        $companyRole = $this->userService->getCompanyRole($companyId);

        $inviteUser = $this->userService->getUserInvite($companyId);
        // $userDataWithIsInvalid = $this->userService->getStatusEmployerIsValid();
        return view('frontend.pages.employer.user', compact('data', 'companyRole', 'inviteUser', 'arrLang'));
    }

    public function usersRole(Request $request)
    {
        $lang = app()->getLocale();
        $str = 'employer_user';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        $companyId = auth('client')->user()->company_id;
        $data = $this->userService->getUserRoleCompany($companyId);
        $roles = config('constant.role_employee_permission');
        return view('frontend.pages.employer.role', compact('data', 'arrLang', 'roles'));
    }

    public function createUsers(Request $request)
    {
        $this->userService->createUser($request->all());
        Toast::success(__('message.resend_email'));
        return redirect()->route('employer-users');
    }

    public function verifyEmployerInvite(Request $request)
    {
        $data = $this->userService->verifyEmailInvite($request->all());
        if (!$data) {
            Toast::warning(__('message.verify_account_fail'));

            $type = 'LoginRequest';
            return redirect()->route('employer-dashboard')->with(compact('type'));
        }

        return view('frontend.pages.employer.verify-register', compact('data'));
    }

    public function changeInfoCompany(Request $request)
    {
        $companyId = auth('client')->user()->company_id;
        $this->companyService->updateCompany($companyId, $request->all());

        Toast::success(__('message.edit_success'));

        return back();
    }

    public function registerEmployerInvite(RegisterInviteRequest $request)
    {
        $result = $this->userService->updateUserInvite($request->all());

        if ($result) {
            Toast::success(__('message.verify_account_success'));
        } else {
            Toast::warning(__('message.verify_account_fail'));
        }

        $type = 'LoginRequest';
        return redirect()->route('employer-dashboard')->with(compact('type'));
    }

    protected function checkShowData($typeBonus, $status): bool
    {
        $keys_to_select = array('accepted', 'pass-interview', 'fail-interview');
        $selected_array = array_intersect_key(config('constant.submit_cvs_status_value'), array_flip($keys_to_select));
        if ($typeBonus == 'cv' && in_array($status, $selected_array)) {
            return false;
        }

        $keys_to_select = array('pass-interview');
        $selected_array = array_intersect_key(config('constant.submit_cvs_status_value'), array_flip($keys_to_select));
        if ($typeBonus == 'interview' && in_array($status, $selected_array)) {
            return false;
        }

        $keys_to_select = array('pass-interview', 'onboarded', 'offering');
        $selected_array = array_intersect_key(config('constant.submit_cvs_status_value'), array_flip($keys_to_select));
        if ($typeBonus == 'onboard' && in_array($status, $selected_array)) {
            return false;
        }
        return true;
    }

    public function cvBought(Request $request)
    {
        $data = $this->wareHouseCvSellingBuyService->getCvBought($request->all());
        // $userDataWithIsInvalid = $this->userService->getStatusEmployerIsValid();
        return view('frontend.pages.employer.cv-bought', ['data' => $data]);
    }

    public function discusses($id)
    {
        $bock = $this->wareHouseCvSellingBuyBookService->getByWarehouseCvBuyId($id);
        $cvSellingBuy = $this->wareHouseCvSellingBuyService->find($id);
        $cvSellingBuy->load('rec');
        $cvSellingBuy->rec->email = '';
        $cvSellingBuy->rec->token = '';
        $cvSellingBuy->rec->mobile = '';
        $cvSellingBuy->rec->provider_id = '';
        $data = [
            'cvSellingBuy'  => $cvSellingBuy,
            'discuss'       => $this->wareHouseCvSellingBuyDiscussService->getByWarehouseCvBuyId($id),
            'book'          => $bock,
            'onboard'       => $this->warehouseCvSellingBuyOnboardService->getByWarehouseCvBuyId($id),
            'messageReject' => $this->wareHouseCvSellingBuyService->getMessageRejectInterview($cvSellingBuy, $bock),
        ];
        return response()->json($data);
    }

    public function discussesSubmit($id)
    {
        $submit = $this->wareHouseSubmitCvService->find($id);
        $submit->load(['rec:id']);
        $submit->code = '';
        // $submit->rec->email = '';
        // $submit->rec->token = '';
        // $submit->rec->mobile = '';
        // $submit->rec->provider_id = '';
        $book = $this->submitCvBookService->getBySubmitCvId($id);
        $data = [
            'submitCv'      => $submit,
            'discuss'       => $this->submitCvDiscussService->getBySubmitId($id),
            'book'          => $book,
            'onboard'       => $this->submitCvOnboardService->getBySubmitCvId($id),
            'messageReject' => $this->submitCvService->getMessageRejectInterview($submit, $book),
            // 'book'          => [],
            // 'onboard'       => [],
            // 'messageReject' => false,
        ];

        // set unread to read
        $this->submitCvDiscussService->setReadByType($id, config('constant.role.employer'));

        return response()->json($data);
    }

    public function sendDiscusses(Request $request)
    {

        try {
            $data = $this->wareHouseCvSellingBuyDiscussService->employerSendDiscuss($request->warehouse_cv_selling_buy_id, $request->comment);
            $response = [
                'success'   =>  true,
                'data' => $data
            ];
            return response()->json($response);
        } catch (\Exception $e) {
            $response = [
                'success'   =>  false
            ];

            return response()->json($response);
        }
    }
    public function sendDiscussesSubmit(Request $request)
    {

        try {
            $data = $this->submitCvDiscussService->employerSendDiscuss($request->submit_id, $request->comment);
            $response = [
                'success'   =>  true,
                'data' => $data
            ];
            return response()->json($response);
        } catch (\Exception $e) {
            $response = [
                'success'   =>  false
            ];

            return response()->json($response);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     * NTD đặt lịch phỏng vấn
     */
    public function scheduleInterview(Request $request)
    {
        try {
            // dd($request->all());
            $this->wareHouseCvSellingBuyBookService->scheduleInterview($request->all());
            Toast::success('Đặt lịch phỏng vấn thành công');
        } catch (\Exception $e) {
            Toast::warning('Đặt lịch phỏng vấn thất bại');
        }
        return redirect()->back();
    }

    public function scheduleInterviewSubmit(Request $request)
    {
        try {
            $this->submitCvBookService->scheduleInterview($request->all());
            Toast::success('Đặt lịch phỏng vấn thành công');
        } catch (\Exception $e) {
            // log error with file name and function name
            Log::error('error: ' . __FILE__ . ': ' . __FUNCTION__, [
                'content: ' => $e->getMessage()
            ]);
            Toast::warning('Đặt lịch phỏng vấn thất bại');
        }
        return redirect()->back();
    }

    public function scheduleOnboard(Request $request)
    {
        try {
            $this->warehouseCvSellingBuyOnboardService->scheduleOnboard($request->all());
            Toast::success('Đặt lịch onboard thành công');
        } catch (\Exception $e) {
            Toast::warning('Đặt lịch onboard thất bại');
        }
        return redirect()->back();
    }



    public function scheduleOnboardSubmit(Request $request)
    {
        try {
            $this->submitCvOnboardService->scheduleOnboard($request->all());
            Toast::success('Đặt lịch onboard thành công');
        } catch (\Exception $e) {
            Toast::warning('Đặt lịch onboard thất bại');
        }
        return redirect()->back();
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     * NTD khiếu nại
     */
    public function complain(Request $request)
    {

        try {
            $this->wareHouseCvSellingBuyService->complain($request->all());
            Toast::success('Khiếu nại thành công');
        } catch (\Exception $e) {
            Toast::warning('Khiếu nại thất bại');
        }
        return redirect()->back();
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     * NTD khiếu nại
     */
    public function complainSubmit(Request $request)
    {

        try {
            $this->submitCvService->complain($request->all());
            Toast::success('Khiếu nại thành công');
        } catch (\Exception $e) {
            Toast::warning('Khiếu nại thất bại');
        }
        return redirect()->back();
    }

    public function warehouseCvSellingBuyChangeStatus(Request $request)
    {
        try {
            $this->wareHouseCvSellingBuyService->warehouseCvSellingBuyChangeStatus($request->all());
            Toast::success('Cập nhật trạng thái thành công');
        } catch (\Exception $e) {
            Toast::warning('Cập nhật trạng thái thất bại');
        }
        return redirect()->back();
    }
    public function submitCvChangeStatus(Request $request)
    {
        try {
            // dd($request->all());
            $this->submitCvService->submitCvChangeStatus($request->all());
            Toast::success('Cập nhật trạng thái thành công');
        } catch (\Exception $e) {
            Toast::warning('Cập nhật trạng thái thất bại');
        }
        return redirect()->back();
    }

    public function paymentHistory(Request $request)
    {
        $data = $this->walletTransactionService->getHistory($request->all());
        $spentThisMonth = $this->walletTransactionService->spentThisMonth();
        $spentThisYear = $this->walletTransactionService->spentThisYear();
        return view('frontend.pages.employer.payment_history', compact('data', 'spentThisMonth', 'spentThisYear'));
    }

    public function paymentInfo()
    {
        $lang = app()->getLocale();
        $str = 'payment_info';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        return view('frontend.pages.employer.payment-info', compact('arrLang'));
    }

    public function wallet(Request $request)
    {
        $transId = $request->get('apptransid');
        $trans = null;
        if ($transId) {
            $zalopayTransactionService = resolve(ZalopayTransactionService::class);
            $trans = $zalopayTransactionService->getTransactionPayment($transId);
        }
        $data = $this->userService->getDeposit($request->all());
        $statical = $this->userService->statisticalDeposit();
        // $userDataWithIsInvalid = $this->userService->getStatusEmployerIsValid();
        return view('frontend.pages.employer.list-deposit', compact('data', 'statical', 'trans'));
    }

    public function deposit(DepositRequest $request)
    {
        try {
            $data = $this->userService->deposit($request->all());
            return response()->json($data);
        } catch (\Exception $e) {
            Log::info('error deposit: ', [
                'content: ' => $e->getMessage()
            ]);
            throw new \Exception('error');
        }
    }

    public function viewPopupChangeStatus($id)
    {
        $sellingBuy = $this->wareHouseCvSellingBuyService->find($id);
        return view('frontend.pages.employer.view-popup-status', compact('sellingBuy'));
    }

    public function viewPopupChangeStatusSubmit($id)
    {
        $submitCv = $this->submitCvService->find($id);
        return view('frontend.pages.employer.view-popup-status-submit', compact('submitCv'));
    }

    public function cancelInterview(Request $request)
    {
        try {
            $this->wareHouseCvSellingBuyService->cancelInterview($request->warehouse_cv_selling_buy_id);
            Toast::success('Huỷ đặt lịch thành công');
        } catch (\Exception $e) {
            Toast::warning('Huỷ đặt lịch thất bại');
        }
        return redirect()->back();
    }
    public function cancelInterviewSubmit(Request $request)
    {
        try {
            $this->submitCvService->cancelInterview($request->submit_cv_id);
            Toast::success('Huỷ đặt lịch thành công');
        } catch (\Exception $e) {
            Toast::warning('Huỷ đặt lịch thất bại');
        }
        return redirect()->back();
    }

    public function confirmPolicy(Request $request)
    {
        try {
            $user = auth('client')->user();
            $this->userService->setConfirmed($user);
            return response()->json(['success' => 1]);
        } catch (\Exception $e) {
            Log::info('error confirm policy: ', [
                'content: ' => $e->getMessage()
            ]);
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Hiển thị landing page đăng ký nhà tuyển dụng
     * 
     * @return View
     */
    public function registerLanding()
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig('employer-landing-register', $lang);

        //banner NTD
        $listBanners = $this->bannerService->getListByType(config('constant.role_frontend_revest.ntd'), strtolower(config('constant.position_banner.home-ntd-banner')));
        //logo công ty

        $arrBanner = [];
        foreach ($listBanners as $item) {
            if ($lang == config('constant.language.vi')) {
                array_push($arrBanner, $item->image_url_vn);
            } else {
                array_push($arrBanner, $item->image_url_en);
            }
        }
        $listCompanies = $this->companyService->getListByHome();
        //testimonations NTD
        $listTestimonations = $this->testimonialService->getListByType(config('constant.role_frontend_revest.ntd'));

        return view('frontend.pages.employer.register_landing_form', compact(
            'listTestimonations',
        ));
    }
}

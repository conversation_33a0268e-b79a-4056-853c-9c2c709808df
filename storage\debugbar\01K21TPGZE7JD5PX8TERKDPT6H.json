{"__meta": {"id": "01K21TPGZE7JD5PX8TERKDPT6H", "datetime": "2025-08-07 15:34:56", "utime": **********.110777, "method": "GET", "uri": "/employer/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 46, "messages": [{"message": "[15:34:55] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.783255, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.78373, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-manager-dashboard' limit 1\\n-- \",\n    \"Time:\": 21.98\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.842557, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local\\/employer\\/dashboard', 'http:\\/\\/recland.local\\/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36 Edg\\\\\\/*********\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1PNUd3UFhlNU5QVUxtaUN3c1U3OXc9PSIsInZhbHVlIjoiMkpPWEhPMi9FTXJMem9Lbk9QUXVqZXQ3L1FicE9DUWcvY2dnVjFEdXNjWDlma0hTbU9CZStub2FOMU5VVFRjeUJwUWJZOVk4VkdTeExza2dPVUtPWDlrMlB3SkZXMkh4K1IwY3RXeWdYUTJOVllYd0hIUUJqb2JIQ0l3eDJNUGQiLCJtYWMiOiJjMGM0ZmJmOWQ4MjQzNjRiNjJjZTkxMzY4OTI2NjdiZGJlNzJmZTQ1ZDVhZGU3MTcxMTRiNGVlYWQ4YjA3ODY5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IndmbEhIUHkvc2xlcWxTcTJ0VUJBc2c9PSIsInZhbHVlIjoiRXlQeVdQR2FTMmNXbXE4UmlvZ2plcitMbWU1U1Nmc3dUWERXZGdVcllvMEQzU2h0V3FiRzJOYzJOVFRVUDFXcUZ1TTBreG9lS2JxZzZBbWd0L3lkVkJQVnQ0WDN0bncwT2ErZDliZHlQNG1qa05HdnlNK3JGeS92SDRzWlJ1dXMiLCJtYWMiOiI5MzkwOWUzNDEzZWRkZWY3NTI2ZDdhNDE2YmUwOWVkNWExOGNhOTQ0ZWU3Mjk4MGZkYWFjZDk1ZmEzNzEzMzUwIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:55', '2025-08-07 15:34:55')\\n-- \",\n    \"Time:\": 0.6\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.881849, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '7490' limit 1\\n-- \",\n    \"Time:\": 0.66\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.890494, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.79\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.896625, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.898966, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 5.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.905955, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '7490'\\n-- \",\n    \"Time:\": 8.02\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.91627, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `status` = '5' and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1'\\n-- \",\n    \"Time:\": 17.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.936869, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `status` != '7' and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1'\\n-- \",\n    \"Time:\": 31.88\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.970553, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '0' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 2.17\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.977508, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '1' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 2.02\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.983114, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '2' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 2.24\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.988925, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '3' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.99\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.994505, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '4' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.87\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.998005, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '8' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.86\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.001576, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '5' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 2.02\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.005161, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '6' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.88\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.008589, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-manager-dashboard' limit 1\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.010243, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1'\\n-- \",\n    \"Time:\": 23.1\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.035061, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `user_id` = '7490' limit 1\\n-- \",\n    \"Time:\": 2.3\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.038818, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `deposits` where `user_id` = '7490'\\n-- \",\n    \"Time:\": 0.26\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.040954, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect sum(`amount`) as aggregate from `deposits` where `user_id` = '7490' and `created_at` between '2025-08-01 00:00:00' and '2025-08-31 23:59:59'\\n-- \",\n    \"Time:\": 0.28\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.043574, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect sum(`amount`) as aggregate from `deposits` where `user_id` = '7490' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59'\\n-- \",\n    \"Time:\": 0.28\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.045084, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '7490'\\n-- \",\n    \"Time:\": 0.27\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.04705, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '7490' and `type` = '0' and year(`created_at`) = '2025' and month(`created_at`) = '08'\\n-- \",\n    \"Time:\": 0.29\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.048763, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '7490' and `type` = '0' and year(`created_at`) = '2025'\\n-- \",\n    \"Time:\": 0.27\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.050237, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.06721, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.06916, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.070712, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.072273, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.073813, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.075381, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.076942, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `meta_data` where `meta_data`.`object_type` = 'App\\\\Models\\\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1\\n-- \",\n    \"Time:\": 0.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.08023, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.84\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.085997, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.087731, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.090244, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.093243, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.094845, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.096425, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.098034, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.09957, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.101219, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:56] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.102851, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.340153, "end": **********.110854, "duration": 0.7707009315490723, "duration_str": "771ms", "measures": [{"label": "Booting", "start": **********.340153, "relative_start": 0, "end": **********.759807, "relative_end": **********.759807, "duration": 0.*****************, "duration_str": "420ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.759817, "relative_start": 0.****************, "end": **********.110857, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "351ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.773491, "relative_start": 0.***************, "end": **********.778936, "relative_end": **********.778936, "duration": 0.005445003509521484, "duration_str": "5.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "1x frontend.pages.employer.dashboard", "param_count": null, "params": [], "start": **********.061509, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/dashboard.blade.phpfrontend.pages.employer.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Femployer%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.employer.dashboard"}, {"name": "1x frontend.layouts.employer.app", "param_count": null, "params": [], "start": **********.064534, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/app.blade.phpfrontend.layouts.employer.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.employer.app"}, {"name": "2x frontend.layouts.user.avatar", "param_count": null, "params": [], "start": **********.065239, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/user/avatar.blade.phpfrontend.layouts.user.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fuser%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.user.avatar"}, {"name": "2x frontend.layouts.employer.menu", "param_count": null, "params": [], "start": **********.06573, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.phpfrontend.layouts.employer.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.employer.menu"}, {"name": "1x frontend.layouts.modal.employer_confirm", "param_count": null, "params": [], "start": **********.078225, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.phpfrontend.layouts.modal.employer_confirm", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmodal%2Femployer_confirm.blade.php&line=1", "ajax": false, "filename": "employer_confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.modal.employer_confirm"}, {"name": "1x frontend.layouts.login.app", "param_count": null, "params": [], "start": **********.081625, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/login/app.blade.phpfrontend.layouts.login.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Flogin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.login.app"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": **********.082493, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.login.header", "param_count": null, "params": [], "start": **********.083044, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.phpfrontend.inc_layouts.login.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.header"}, {"name": "2x frontend.inc_layouts.notification.drop-notification", "param_count": null, "params": [], "start": **********.088815, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/notification/drop-notification.blade.phpfrontend.inc_layouts.notification.drop-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fnotification%2Fdrop-notification.blade.php&line=1", "ajax": false, "filename": "drop-notification.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.inc_layouts.notification.drop-notification"}, {"name": "1x frontend.inc_layouts.login.footer_v2", "param_count": null, "params": [], "start": **********.104243, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/footer_v2.blade.phpfrontend.inc_layouts.login.footer_v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Ffooter_v2.blade.php&line=1", "ajax": false, "filename": "footer_v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.footer_v2"}, {"name": "1x frontend.inc_layouts.login.modal_report", "param_count": null, "params": [], "start": **********.105202, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/modal_report.blade.phpfrontend.inc_layouts.login.modal_report", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fmodal_report.blade.php&line=1", "ajax": false, "filename": "modal_report.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.modal_report"}, {"name": "1x frontend.partials.bug_report_modal", "param_count": null, "params": [], "start": **********.105992, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/partials/bug_report_modal.blade.phpfrontend.partials.bug_report_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fbug_report_modal.blade.php&line=1", "ajax": false, "filename": "bug_report_modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.partials.bug_report_modal"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.107597, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET employer/dashboard", "middleware": "web, localization, visit-website, check-employer, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@dashboard<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=217\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "as": "employer-manager-dashboard", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=217\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:217-278</a>"}, "queries": {"count": 44, "nb_statements": 44, "nb_visible_statements": 44, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13838000000000006, "accumulated_duration_str": "138ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-manager-dashboard' limit 1", "type": "query", "params": [], "bindings": ["employer-manager-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.820884, "duration": 0.02198, "duration_str": "21.98ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "b5743e64e3255a2e102720b7b23123c44f032301e0af811d68647bb6b25f9e64"}, "start_percent": 0, "width_percent": 15.884}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/dashboard', 'http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1PNUd3UFhlNU5QVUxtaUN3c1U3OXc9PSIsInZhbHVlIjoiMkpPWEhPMi9FTXJMem9Lbk9QUXVqZXQ3L1FicE9DUWcvY2dnVjFEdXNjWDlma0hTbU9CZStub2FOMU5VVFRjeUJwUWJZOVk4VkdTeExza2dPVUtPWDlrMlB3SkZXMkh4K1IwY3RXeWdYUTJOVllYd0hIUUJqb2JIQ0l3eDJNUGQiLCJtYWMiOiJjMGM0ZmJmOWQ4MjQzNjRiNjJjZTkxMzY4OTI2NjdiZGJlNzJmZTQ1ZDVhZGU3MTcxMTRiNGVlYWQ4YjA3ODY5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IndmbEhIUHkvc2xlcWxTcTJ0VUJBc2c9PSIsInZhbHVlIjoiRXlQeVdQR2FTMmNXbXE4UmlvZ2plcitMbWU1U1Nmc3dUWERXZGdVcllvMEQzU2h0V3FiRzJOYzJOVFRVUDFXcUZ1TTBreG9lS2JxZzZBbWd0L3lkVkJQVnQ0WDN0bncwT2ErZDliZHlQNG1qa05HdnlNK3JGeS92SDRzWlJ1dXMiLCJtYWMiOiI5MzkwOWUzNDEzZWRkZWY3NTI2ZDdhNDE2YmUwOWVkNWExOGNhOTQ0ZWU3Mjk4MGZkYWFjZDk1ZmEzNzEzMzUwIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:55', '2025-08-07 15:34:55')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local/employer/dashboard", "http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk", "[\"en-us\",\"en\",\"vi\",\"nl\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1PNUd3UFhlNU5QVUxtaUN3c1U3OXc9PSIsInZhbHVlIjoiMkpPWEhPMi9FTXJMem9Lbk9QUXVqZXQ3L1FicE9DUWcvY2dnVjFEdXNjWDlma0hTbU9CZStub2FOMU5VVFRjeUJwUWJZOVk4VkdTeExza2dPVUtPWDlrMlB3SkZXMkh4K1IwY3RXeWdYUTJOVllYd0hIUUJqb2JIQ0l3eDJNUGQiLCJtYWMiOiJjMGM0ZmJmOWQ4MjQzNjRiNjJjZTkxMzY4OTI2NjdiZGJlNzJmZTQ1ZDVhZGU3MTcxMTRiNGVlYWQ4YjA3ODY5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IndmbEhIUHkvc2xlcWxTcTJ0VUJBc2c9PSIsInZhbHVlIjoiRXlQeVdQR2FTMmNXbXE4UmlvZ2plcitMbWU1U1Nmc3dUWERXZGdVcllvMEQzU2h0V3FiRzJOYzJOVFRVUDFXcUZ1TTBreG9lS2JxZzZBbWd0L3lkVkJQVnQ0WDN0bncwT2ErZDliZHlQNG1qa05HdnlNK3JGeS92SDRzWlJ1dXMiLCJtYWMiOiI5MzkwOWUzNDEzZWRkZWY3NTI2ZDdhNDE2YmUwOWVkNWExOGNhOTQ0ZWU3Mjk4MGZkYWFjZDk1ZmEzNzEzMzUwIiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Edge", "127.0.0.1", null, null, "2025-08-07 15:34:55", "2025-08-07 15:34:55"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.8813238, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 15.884, "width_percent": 0.434}, {"sql": "select * from `users` where `id` = 7490 limit 1", "type": "query", "params": [], "bindings": [7490], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.889904, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "46fb2a7e2da69ce8dd6b76eee0f083f3fd38c5f3e291e80b6e2a5dd1df6426fd"}, "start_percent": 16.317, "width_percent": 0.477}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 21, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.8959038, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 16.794, "width_percent": 0.571}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 26, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 28, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.898715, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 17.365, "width_percent": 0.231}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7490 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7490], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 22, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}], "start": **********.900707, "duration": 0.00532, "duration_str": "5.32ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:125", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=125", "ajax": false, "filename": "EmployerController.php", "line": "125"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "2acb24b13514507d20205de68fbb9c1fa196748160210213f26fc2003be4b8af"}, "start_percent": 17.596, "width_percent": 3.844}, {"sql": "select count(*) as aggregate from `job` where `is_active` = 1 and `employer_id` = 7490", "type": "query", "params": [], "bindings": [1, 7490], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 389}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 112}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 222}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.908322, "duration": 0.00802, "duration_str": "8.02ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:389", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 389}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=389", "ajax": false, "filename": "JobRepository.php", "line": "389"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `is_active` = ? and `employer_id` = ?", "hash": "8da02c8d5f32aa8cb3bc3bdd5efaba1d49fcb10351f101c3192eda543dae20e9"}, "start_percent": 21.441, "width_percent": 5.796}, {"sql": "select count(*) as aggregate from `submit_cvs` where `status` = 5 and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1", "type": "query", "params": [], "bindings": [5, 7490, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 627}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 227}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9191809, "duration": 0.01776, "duration_str": "17.76ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=75", "ajax": false, "filename": "SubmitCvRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `status` = ? and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ?", "hash": "21d23c5cc43192a6617dbb8928864592bd348b9c510492d6a7a8f32eb5bf9d53"}, "start_percent": 27.237, "width_percent": 12.834}, {"sql": "select count(*) as aggregate from `submit_cvs` where `status` != 7 and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1", "type": "query", "params": [], "bindings": [7, 7490, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 627}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.938854, "duration": 0.03188, "duration_str": "31.88ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=75", "ajax": false, "filename": "SubmitCvRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `status` != ? and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ?", "hash": "0263cffa67244ff50c552b4cebc0c1cdf00cad21f76a926bafe0296239e573aa"}, "start_percent": 40.071, "width_percent": 23.038}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1 and `status` = 0 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [7490, 1, 0, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9754899, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "09fc27d4642a1da8eae469abd6d9d1158b22e575bc3ecf7ceb75cba0de332c63"}, "start_percent": 63.109, "width_percent": 1.568}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1 and `status` = 1 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [7490, 1, 1, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.981245, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "feac201cba4bd23a18e67a21dcefda5235ed33d38a31b5d1b9012342a8e2dbcf"}, "start_percent": 64.677, "width_percent": 1.46}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1 and `status` = 2 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [7490, 1, 2, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.98684, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "452d77be9f70619b854ea4779c346c32c9d79a0ecfd954e503c0f9c105f1816d"}, "start_percent": 66.137, "width_percent": 1.619}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1 and `status` = 3 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [7490, 1, 3, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9926639, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "ff81bae03429b177f0a949267324e3d91fdb0735114d73b06ff9fd8259f42c9f"}, "start_percent": 67.755, "width_percent": 1.438}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1 and `status` = 4 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [7490, 1, 4, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.99621, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "44790f75aa7577624e9ea3bcf4f650009a5229f4c72eed04e7a30e01daa7e887"}, "start_percent": 69.194, "width_percent": 1.351}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1 and `status` = 8 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [7490, 1, 8, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.999784, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "ae34b91608c9e44eafd6111cc40c1802624910e068780088c37f8a847ed80ddd"}, "start_percent": 70.545, "width_percent": 1.344}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1 and `status` = 5 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [7490, 1, 5, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.003209, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "bce315b9aa526c6e4b6e27fd194c86681f11de80b274fc7f2165c6643086ed58"}, "start_percent": 71.889, "width_percent": 1.46}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1 and `status` = 6 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [7490, 1, 6, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.006777, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "54db08c8705c65d7241627155055c5ba472232a6a42ff5e81c60dbeb2efbe243"}, "start_percent": 73.349, "width_percent": 1.359}, {"sql": "select * from `seos` where `key` = 'employer-manager-dashboard' limit 1", "type": "query", "params": [], "bindings": ["employer-manager-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 240}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.00998, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "b5743e64e3255a2e102720b7b23123c44f032301e0af811d68647bb6b25f9e64"}, "start_percent": 74.707, "width_percent": 0.238}, {"sql": "select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 7490) and `is_active` = 1", "type": "query", "params": [], "bindings": [7490, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Admin/SubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\SubmitCvService.php", "line": 1153}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 244}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0120592, "duration": 0.023100000000000002, "duration_str": "23.1ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=75", "ajax": false, "filename": "SubmitCvRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ?", "hash": "3daacedab7d18752b724e3f9ca2617960479861d2c185cd92d2c124feeaee14e"}, "start_percent": 74.946, "width_percent": 16.693}, {"sql": "select * from `wallets` where `user_id` = 7490 limit 1", "type": "query", "params": [], "bindings": [7490], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WalletRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WalletRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 248}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.036585, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "WalletRepository.php:28", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WalletRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WalletRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWalletRepository.php&line=28", "ajax": false, "filename": "WalletRepository.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `user_id` = ? limit 1", "hash": "5632ffbff143184b1c0483da0aa91e0d7c538b800075dfa39f6b8516806506c5"}, "start_percent": 91.639, "width_percent": 1.662}, {"sql": "select count(*) as aggregate from `deposits` where `user_id` = 7490", "type": "query", "params": [], "bindings": [7490], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 250}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.040762, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "DepositRepository.php:41", "source": {"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FDepositRepository.php&line=41", "ajax": false, "filename": "DepositRepository.php", "line": "41"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `deposits` where `user_id` = ?", "hash": "63edd932dc54424a9976e6534db9ad0c840685ffeb9af05d11beafd142443a82"}, "start_percent": 93.301, "width_percent": 0.188}, {"sql": "select sum(`amount`) as aggregate from `deposits` where `user_id` = 7490 and `created_at` between '2025-08-01 00:00:00' and '2025-08-31 23:59:59'", "type": "query", "params": [], "bindings": [7490, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 46}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 729}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 251}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0433671, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DepositRepository.php:46", "source": {"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FDepositRepository.php&line=46", "ajax": false, "filename": "DepositRepository.php", "line": "46"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select sum(`amount`) as aggregate from `deposits` where `user_id` = ? and `created_at` between ? and ?", "hash": "63eb1ae2897c07e93394eddc321d788526cb29012f57b2e1ce0ada20a1340e27"}, "start_percent": 93.489, "width_percent": 0.202}, {"sql": "select sum(`amount`) as aggregate from `deposits` where `user_id` = 7490 and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59'", "type": "query", "params": [], "bindings": [7490, "2025-01-01 00:00:00", "2025-12-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 46}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 730}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 251}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.044871, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DepositRepository.php:46", "source": {"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FDepositRepository.php&line=46", "ajax": false, "filename": "DepositRepository.php", "line": "46"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select sum(`amount`) as aggregate from `deposits` where `user_id` = ? and `created_at` between ? and ?", "hash": "19a2fefd413b51c072a62edcb2cc1a3fc0fe8cae38b62a288faee1b5730612f6"}, "start_percent": 93.691, "width_percent": 0.202}, {"sql": "select count(*) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = 7490", "type": "query", "params": [], "bindings": [7490], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 26}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 253}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0468562, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:44", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=44", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "44"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = ?", "hash": "4040e6f7b49423f1fbf6547716d69c875d106ffd4c9961ce4cb96f04ae678953"}, "start_percent": 93.894, "width_percent": 0.195}, {"sql": "select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = 7490 and `type` = 0 and year(`created_at`) = 2025 and month(`created_at`) = '08'", "type": "query", "params": [], "bindings": [7490, 0, 2025, "08"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 254}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0485392, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:56", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=56", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "56"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = ? and `type` = ? and year(`created_at`) = ? and month(`created_at`) = ?", "hash": "a43a49619b9bcab3df59e1fd0e11eb538db7c1eb6ee7bebbe3b73026d60ec297"}, "start_percent": 94.089, "width_percent": 0.21}, {"sql": "select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = 7490 and `type` = 0 and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": [7490, 0, 2025], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 36}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 255}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0500321, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=75", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = ? and `type` = ? and year(`created_at`) = ?", "hash": "27208b7f7bdfdeb2427378be690d00b90db7a674c3dfa1932454a00a036d7206"}, "start_percent": 94.298, "width_percent": 0.195}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.0668402, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 94.493, "width_percent": 0.318}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.0688279, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 94.811, "width_percent": 0.289}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.0703878, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 95.1, "width_percent": 0.282}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.071948, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 95.382, "width_percent": 0.282}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.073497, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 95.664, "width_percent": 0.275}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.0750358, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 95.939, "width_percent": 0.296}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.076618, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 96.235, "width_percent": 0.282}, {"sql": "select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = 7490 and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 7490, "employer_confirmed_at"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, {"index": 19, "namespace": "view", "name": "frontend.layouts.modal.employer_confirm", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.php", "line": 5}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.0798688, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "User.php:197", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=197", "ajax": false, "filename": "User.php", "line": "197"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `meta_data` where `meta_data`.`object_type` = ? and `meta_data`.`object_id` = ? and `meta_data`.`object_id` is not null and `key` = ? limit 1", "hash": "c1027118294dd0a667dd167a5d2360e5e721332b18f22b6ea04e67176508a422"}, "start_percent": 96.517, "width_percent": 0.311}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 7490 and `notifications`.`notifiable_id` is not null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 7490], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.085248, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:98", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=98", "ajax": false, "filename": "header.blade.php", "line": "98"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null order by `created_at` desc", "hash": "e4fea375135a93679975c14bd461e5b822cc3df34bc0e70d04f9e664a10a8c6a"}, "start_percent": 96.828, "width_percent": 0.607}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 7490 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 7490], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.087471, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:99", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=99", "ajax": false, "filename": "header.blade.php", "line": "99"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "hash": "e363911ffc4721973d884b5a040403f6ef56d5e8d48ec406063838d6a6f29342"}, "start_percent": 97.435, "width_percent": 0.238}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.0899138, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:243", "source": {"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=243", "ajax": false, "filename": "header.blade.php", "line": "243"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 97.673, "width_percent": 0.289}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.092902, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 97.962, "width_percent": 0.296}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.094512, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 98.258, "width_percent": 0.289}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.096082, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 98.547, "width_percent": 0.296}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.0977159, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 98.844, "width_percent": 0.282}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.099257, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 99.126, "width_percent": 0.275}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.1008868, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 99.4, "width_percent": 0.289}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.102491, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 99.689, "width_percent": 0.311}]}, "models": {"data": {"App\\Models\\EmployerType": {"retrieved": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmployerType.php&line=1", "ajax": false, "filename": "EmployerType.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 22, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 21, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7490"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/employer/dashboard", "action_name": "employer-manager-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@dashboard", "uri": "GET employer/dashboard", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@dashboard<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=217\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=217\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:217-278</a>", "middleware": "web, localization, visit-website, check-employer", "duration": "776ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1696058544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1696058544\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-144203133 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-144203133\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1969412767 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"91 characters\">http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1PNUd3UFhlNU5QVUxtaUN3c1U3OXc9PSIsInZhbHVlIjoiMkpPWEhPMi9FTXJMem9Lbk9QUXVqZXQ3L1FicE9DUWcvY2dnVjFEdXNjWDlma0hTbU9CZStub2FOMU5VVFRjeUJwUWJZOVk4VkdTeExza2dPVUtPWDlrMlB3SkZXMkh4K1IwY3RXeWdYUTJOVllYd0hIUUJqb2JIQ0l3eDJNUGQiLCJtYWMiOiJjMGM0ZmJmOWQ4MjQzNjRiNjJjZTkxMzY4OTI2NjdiZGJlNzJmZTQ1ZDVhZGU3MTcxMTRiNGVlYWQ4YjA3ODY5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IndmbEhIUHkvc2xlcWxTcTJ0VUJBc2c9PSIsInZhbHVlIjoiRXlQeVdQR2FTMmNXbXE4UmlvZ2plcitMbWU1U1Nmc3dUWERXZGdVcllvMEQzU2h0V3FiRzJOYzJOVFRVUDFXcUZ1TTBreG9lS2JxZzZBbWd0L3lkVkJQVnQ0WDN0bncwT2ErZDliZHlQNG1qa05HdnlNK3JGeS92SDRzWlJ1dXMiLCJtYWMiOiI5MzkwOWUzNDEzZWRkZWY3NTI2ZDdhNDE2YmUwOWVkNWExOGNhOTQ0ZWU3Mjk4MGZkYWFjZDk1ZmEzNzEzMzUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969412767\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-653827749 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">J9m30nlyDaWRKw7nMx9mDA5J2JdNAotbwYLo7eQ1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653827749\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-6192229 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 08:34:56 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6192229\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-981737180 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"91 characters\">http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7490</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-981737180\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/employer/dashboard", "action_name": "employer-manager-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@dashboard"}, "badge": null}}
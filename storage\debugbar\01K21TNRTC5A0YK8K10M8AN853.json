{"__meta": {"id": "01K21TNRTC5A0YK8K10M8AN853", "datetime": "2025-08-07 15:34:31", "utime": **********.373111, "method": "POST", "uri": "/employer/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 10, "messages": [{"message": "[15:34:31] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.06399, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:31] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.064431, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"password\\\":\\\"111111\\\"}', 'http:\\/\\/recland.local\\/employer\\/login', 'http:\\/\\/recland.local\\/employer', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHT<PERSON>, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"92\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\"],\\\"content-type\\\":[\\\"application\\\\\\/x-www-form-urlencoded\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36 Edg\\\\\\/*********\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlZaVdOZ2RhdUhOZmU5OE5YQU9FNXc9PSIsInZhbHVlIjoiZitJK09kWTZUM2JuOEtpbDh4QURFK2h6R09oZER2RUpIeDFCYjB2WDR6ZUZqY1lMZFFiUndRVHM1RDNqQzVIT2FOVm5iUjFQSEhjc1BMNGpUOXo3d1BsdFhLSGxtZTJUZFBzSjYzMWdCaWV6dTBSb2UyRk13Y3JNSnR6MTNFTHAiLCJtYWMiOiI2MGRjZWE1NDAwYjdiOGMwNTZiOTAyYjJkODU0M2U2NmUwZmExNzM4YzkwY2NjYzkwOGUwYTQ4NWMzMzEwYmIwIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im40TkFoWUdZci9pOG9oeDFKR2pDbnc9PSIsInZhbHVlIjoibHMrU2hmUnB0K0RJNHRVWEV4N2liZW5NSWZuUCtYLzQvSWZYYnhyejVXWUNYK2hnaUFSSE1ZUzFVd3VlbGJqZWFTNFQzVUNNdmtDejc2WFM0d3J2Y0J4dEVaajVnUUdGYWVORklUVWxWRHJ0T1g1c2tWazJsYTg3OU05cXQ3Z3ciLCJtYWMiOiIyY2Y5NDY1NDJmMWYwOGMyZDNkNWY1ODZjMzQ0MGZmOWJlMGU3ZjUyMTIyYzgyMTkxOWIyZjcyZDI0M2ZmOGY3IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:31', '2025-08-07 15:34:31')\\n-- \",\n    \"Time:\": 17.08\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.134576, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1\\n-- \",\n    \"Time:\": 1\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.160385, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `email` = '<EMAIL>' and `type` = 'employer' and `is_active` = '1' limit 1\\n-- \",\n    \"Time:\": 0.72\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.240425, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nupdate `users` set `last_login_at` = '2025-08-07 15:34:31', `users`.`updated_at` = '2025-08-07 15:34:31' where `id` = '7490'\\n-- \",\n    \"Time:\": 1.11\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.348558, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 0.64\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.352428, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"last_login_at\\\":\\\"2025-08-07 14:30:29\\\"}', '{\\\"last_login_at\\\":\\\"2025-08-07 15:34:31\\\"}', 'updated', '7490', 'App\\\\Models\\\\User', '1', 'App\\\\Models\\\\User', '', '127.0.0.1', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', 'http:\\/\\/recland.local\\/employer\\/login', '2025-08-07 15:34:31', '2025-08-07 15:34:31')\\n-- \",\n    \"Time:\": 0.8\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.359204, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ndelete from `personal_access_tokens` where `personal_access_tokens`.`tokenable_type` = 'App\\\\Models\\\\User' and `personal_access_tokens`.`tokenable_id` = '7490' and `personal_access_tokens`.`tokenable_id` is not null\\n-- \",\n    \"Time:\": 0.87\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.362136, "xdebug_link": null, "collector": "log"}, {"message": "[15:34:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('Personal Access Token', '3948dcd49ade825eb0db9be001611514314d49d3bfbbf75fa7bd71b7d38bb61a', '[\\\"*\\\"]', '7490', 'App\\\\Models\\\\User', '2025-08-07 15:34:31', '2025-08-07 15:34:31')\\n-- \",\n    \"Time:\": 1.01\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.364618, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754555670.615167, "end": **********.373137, "duration": 0.7579700946807861, "duration_str": "758ms", "measures": [{"label": "Booting", "start": 1754555670.615167, "relative_start": 0, "end": **********.036675, "relative_end": **********.036675, "duration": 0.****************, "duration_str": "422ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.036685, "relative_start": 0.*****************, "end": **********.373139, "relative_end": 1.9073486328125e-06, "duration": 0.***************, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.050089, "relative_start": 0.****************, "end": **********.054155, "relative_end": **********.054155, "duration": 0.0040662288665771484, "duration_str": "4.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST employer/login", "middleware": "web, localization, visit-website", "controller": "App\\Http\\Controllers\\Frontend\\UserController@employerPostLogin<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=201\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "as": "employer-post-login", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=201\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserController.php:201-214</a>"}, "queries": {"count": 9, "nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04657, "accumulated_duration_str": "46.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"password\\\":\\\"111111\\\"}', 'http://recland.local/employer/login', 'http://recland.local/employer', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"92\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\/\\/recland.local\\\"],\\\"content-type\\\":[\\\"application\\/x-www-form-urlencoded\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlZaVdOZ2RhdUhOZmU5OE5YQU9FNXc9PSIsInZhbHVlIjoiZitJK09kWTZUM2JuOEtpbDh4QURFK2h6R09oZER2RUpIeDFCYjB2WDR6ZUZqY1lMZFFiUndRVHM1RDNqQzVIT2FOVm5iUjFQSEhjc1BMNGpUOXo3d1BsdFhLSGxtZTJUZFBzSjYzMWdCaWV6dTBSb2UyRk13Y3JNSnR6MTNFTHAiLCJtYWMiOiI2MGRjZWE1NDAwYjdiOGMwNTZiOTAyYjJkODU0M2U2NmUwZmExNzM4YzkwY2NjYzkwOGUwYTQ4NWMzMzEwYmIwIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im40TkFoWUdZci9pOG9oeDFKR2pDbnc9PSIsInZhbHVlIjoibHMrU2hmUnB0K0RJNHRVWEV4N2liZW5NSWZuUCtYLzQvSWZYYnhyejVXWUNYK2hnaUFSSE1ZUzFVd3VlbGJqZWFTNFQzVUNNdmtDejc2WFM0d3J2Y0J4dEVaajVnUUdGYWVORklUVWxWRHJ0T1g1c2tWazJsYTg3OU05cXQ3Z3ciLCJtYWMiOiIyY2Y5NDY1NDJmMWYwOGMyZDNkNWY1ODZjMzQ0MGZmOWJlMGU3ZjUyMTIyYzgyMTkxOWIyZjcyZDI0M2ZmOGY3IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:31', '2025-08-07 15:34:31')", "type": "query", "params": [], "bindings": ["POST", "{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"email\":\"<EMAIL>\",\"password\":\"111111\"}", "http://recland.local/employer/login", "http://recland.local/employer", "[\"en-us\",\"en\",\"vi\",\"nl\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"92\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlZaVdOZ2RhdUhOZmU5OE5YQU9FNXc9PSIsInZhbHVlIjoiZitJK09kWTZUM2JuOEtpbDh4QURFK2h6R09oZER2RUpIeDFCYjB2WDR6ZUZqY1lMZFFiUndRVHM1RDNqQzVIT2FOVm5iUjFQSEhjc1BMNGpUOXo3d1BsdFhLSGxtZTJUZFBzSjYzMWdCaWV6dTBSb2UyRk13Y3JNSnR6MTNFTHAiLCJtYWMiOiI2MGRjZWE1NDAwYjdiOGMwNTZiOTAyYjJkODU0M2U2NmUwZmExNzM4YzkwY2NjYzkwOGUwYTQ4NWMzMzEwYmIwIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im40TkFoWUdZci9pOG9oeDFKR2pDbnc9PSIsInZhbHVlIjoibHMrU2hmUnB0K0RJNHRVWEV4N2liZW5NSWZuUCtYLzQvSWZYYnhyejVXWUNYK2hnaUFSSE1ZUzFVd3VlbGJqZWFTNFQzVUNNdmtDejc2WFM0d3J2Y0J4dEVaajVnUUdGYWVORklUVWxWRHJ0T1g1c2tWazJsYTg3OU05cXQ3Z3ciLCJtYWMiOiIyY2Y5NDY1NDJmMWYwOGMyZDNkNWY1ODZjMzQ0MGZmOWJlMGU3ZjUyMTIyYzgyMTkxOWIyZjcyZDI0M2ZmOGY3IiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Edge", "127.0.0.1", null, null, "2025-08-07 15:34:31", "2025-08-07 15:34:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.117807, "duration": 0.017079999999999998, "duration_str": "17.08ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 0, "width_percent": 36.676}, {"sql": "select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>", "employer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.159457, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:79", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=79", "ajax": false, "filename": "UserRepository.php", "line": "79"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `email` = ? and `type` = ? limit 1", "hash": "3356f8a5761093e91fac88cf0afa097acdd54119d2f91cb9ebc62de1996b5e53"}, "start_percent": 36.676, "width_percent": 2.147}, {"sql": "select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>", "employer", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 112}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.23979, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `email` = ? and `type` = ? and `is_active` = ? limit 1", "hash": "1e527164b66e107697cf934fbe77a31b28fcf148cea2671ed6ff8f6e4ea94b92"}, "start_percent": 38.823, "width_percent": 1.546}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'users'", "type": "query", "params": [], "bindings": ["hri_recland_product", "users"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.320373, "duration": 0.02334, "duration_str": "23.34ms", "memory": 0, "memory_str": null, "filename": "UserService.php:119", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FUserService.php&line=119", "ajax": false, "filename": "UserService.php", "line": "119"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "2f932a8ab5559dc2e718e2169d292b7c251235f387a3834f8b6fb4b996f87912"}, "start_percent": 40.369, "width_percent": 50.118}, {"sql": "update `users` set `last_login_at` = '2025-08-07 15:34:31', `users`.`updated_at` = '2025-08-07 15:34:31' where `id` = 7490", "type": "query", "params": [], "bindings": ["2025-08-07 15:34:31", "2025-08-07 15:34:31", 7490], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.347529, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "UserService.php:119", "source": {"index": 14, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FUserService.php&line=119", "ajax": false, "filename": "UserService.php", "line": "119"}, "connection": "hri_recland_product", "explain": null, "start_percent": 90.487, "width_percent": 2.384}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Resolvers/UserResolver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Resolvers\\UserResolver.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditable.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditable.php", "line": 409}], "start": **********.3518572, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 92.871, "width_percent": 1.374}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"last_login_at\\\":\\\"2025-08-07 14:30:29\\\"}', '{\\\"last_login_at\\\":\\\"2025-08-07 15:34:31\\\"}', 'updated', 7490, 'App\\Models\\User', 1, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/employer/login', '2025-08-07 15:34:31', '2025-08-07 15:34:31')", "type": "query", "params": [], "bindings": ["{\"last_login_at\":\"2025-08-07 14:30:29\"}", "{\"last_login_at\":\"2025-08-07 15:34:31\"}", "updated", 7490, "App\\Models\\User", 1, "App\\Models\\User", null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "http://recland.local/employer/login", "2025-08-07 15:34:31", "2025-08-07 15:34:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 49}, {"index": 33, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}], "start": **********.358474, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Auditor.php:83", "source": {"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FAuditor.php&line=83", "ajax": false, "filename": "Auditor.php", "line": "83"}, "connection": "hri_recland_product", "explain": null, "start_percent": 94.245, "width_percent": 1.718}, {"sql": "delete from `personal_access_tokens` where `personal_access_tokens`.`tokenable_type` = 'App\\Models\\User' and `personal_access_tokens`.`tokenable_id` = 7490 and `personal_access_tokens`.`tokenable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 7490], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 121}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.361338, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "UserService.php:121", "source": {"index": 14, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FUserService.php&line=121", "ajax": false, "filename": "UserService.php", "line": "121"}, "connection": "hri_recland_product", "explain": null, "start_percent": 95.963, "width_percent": 1.868}, {"sql": "insert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('Personal Access Token', '3948dcd49ade825eb0db9be001611514314d49d3bfbbf75fa7bd71b7d38bb61a', '[\\\"*\\\"]', 7490, 'App\\Models\\User', '2025-08-07 15:34:31', '2025-08-07 15:34:31')", "type": "query", "params": [], "bindings": ["Personal Access Token", "3948dcd49ade825eb0db9be001611514314d49d3bfbbf75fa7bd71b7d38bb61a", "[\"*\"]", 7490, "App\\Models\\User", "2025-08-07 15:34:31", "2025-08-07 15:34:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 46}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 122}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3636801, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasApiTokens.php:46", "source": {"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FHasApiTokens.php&line=46", "ajax": false, "filename": "HasApiTokens.php", "line": "46"}, "connection": "hri_recland_product", "explain": null, "start_percent": 97.831, "width_percent": 2.169}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 3, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "OwenIt\\Auditing\\Models\\Audit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FModels%2FAudit.php&line=1", "ajax": false, "filename": "Audit.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}}, "count": 7, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 3, "retrieved": 3, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7490", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "http://recland.local/employer/login", "action_name": "employer-post-login", "controller_action": "App\\Http\\Controllers\\Frontend\\UserController@employerPostLogin", "uri": "POST employer/login", "controller": "App\\Http\\Controllers\\Frontend\\UserController@employerPostLogin<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=201\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=201\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserController.php:201-214</a>", "middleware": "web, localization, visit-website", "duration": "758ms", "peak_memory": "46MB", "response": "Redirect to http://recland.local/employer/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1204904485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1204904485\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1864993547 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864993547\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">92</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://recland.local/employer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlZaVdOZ2RhdUhOZmU5OE5YQU9FNXc9PSIsInZhbHVlIjoiZitJK09kWTZUM2JuOEtpbDh4QURFK2h6R09oZER2RUpIeDFCYjB2WDR6ZUZqY1lMZFFiUndRVHM1RDNqQzVIT2FOVm5iUjFQSEhjc1BMNGpUOXo3d1BsdFhLSGxtZTJUZFBzSjYzMWdCaWV6dTBSb2UyRk13Y3JNSnR6MTNFTHAiLCJtYWMiOiI2MGRjZWE1NDAwYjdiOGMwNTZiOTAyYjJkODU0M2U2NmUwZmExNzM4YzkwY2NjYzkwOGUwYTQ4NWMzMzEwYmIwIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im40TkFoWUdZci9pOG9oeDFKR2pDbnc9PSIsInZhbHVlIjoibHMrU2hmUnB0K0RJNHRVWEV4N2liZW5NSWZuUCtYLzQvSWZYYnhyejVXWUNYK2hnaUFSSE1ZUzFVd3VlbGJqZWFTNFQzVUNNdmtDejc2WFM0d3J2Y0J4dEVaajVnUUdGYWVORklUVWxWRHJ0T1g1c2tWazJsYTg3OU05cXQ3Z3ciLCJtYWMiOiIyY2Y5NDY1NDJmMWYwOGMyZDNkNWY1ODZjMzQ0MGZmOWJlMGU3ZjUyMTIyYzgyMTkxOWIyZjcyZDI0M2ZmOGY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pueeXwEzr1acgKc7V4faWcjl58Z97qy6xnixJ1jx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-385438908 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 08:34:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://recland.local/employer/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385438908\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1836715065 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://recland.local/employer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7490</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836715065\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://recland.local/employer/login", "action_name": "employer-post-login", "controller_action": "App\\Http\\Controllers\\Frontend\\UserController@employerPostLogin"}, "badge": "302 Found"}}
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":115.11} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":3.38} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":4.13} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":3.55} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":3.53} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":19.57} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":2.86} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":4.88} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":2.31} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":1.82} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":2.02} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":2.25} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":1.82} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":1.97} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":1.98} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":2.36} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":2.1} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":3.49} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":3.62} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":7.35} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":3.77} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":3.21} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":3.25} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":3.33} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":2.88} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":3.57} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":1.86} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":1.73} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":1.73} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":1.55} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":1.61} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":1.58} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":29.85} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":9.94} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":10.47} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":9.11} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":11.36} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":16.07} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":25.06} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":12.25} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":21.7} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":12.72} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":11.71} 
[2025-08-07 11:44:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":10.86} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":58.71} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":10.32} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":11.41} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":10.16} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":9.75} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":9.15} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":9.01} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":17.6} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":14.21} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":9.02} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":14.75} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":11.68} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":12.88} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":9.48} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":10.14} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":1.95} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":32.11} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":1.94} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":11.17} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":10.1} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":14.51} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":11.32} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":10.98} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":10.24} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":11.86} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":20.94} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":9.94} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":9.04} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":8.99} 
[2025-08-07 11:44:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":9.39} 
[2025-08-07 13:59:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":4.66} 
[2025-08-07 13:59:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.28} 
[2025-08-07 13:59:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.33} 
[2025-08-07 13:59:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.27} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.43} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.27} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.39} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":1.5} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.44} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.42} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.28} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.3} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.23} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.24} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.41} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.5} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.49} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.54} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.51} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":2.55} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.48} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.38} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.41} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.43} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.41} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.42} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.34} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.26} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.26} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.24} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.3} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.33} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.3} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.3} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.34} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.27} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.48} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.53} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.42} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.28} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.25} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.24} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.23} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":0.91} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.44} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.41} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.41} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.4} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.47} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.46} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.4} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.4} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.45} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.41} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.44} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.61} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.47} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.27} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.39} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.3} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.33} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.33} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.37} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.42} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.37} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.31} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.35} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.39} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.33} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.31} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.28} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.42} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.4} 
[2025-08-07 13:59:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.43} 
[2025-08-07 13:59:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select `value`, `key` from `settings`
-- ","Time:":12.71} 
[2025-08-07 13:59:30] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local', '', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 13:59:30', '2025-08-07 13:59:30')
-- ","Time:":2.12} 
[2025-08-07 13:59:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'home' limit 1
-- ","Time:":0.59} 
[2025-08-07 13:59:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '0' and `is_active` = '1'
-- ","Time:":0.76} 
[2025-08-07 13:59:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` order by `amount` desc
-- ","Time:":0.68} 
[2025-08-07 13:59:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` order by `id` desc limit 1
-- ","Time:":1.73} 
[2025-08-07 13:59:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.64} 
[2025-08-07 13:59:35] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"skill\":null,\"lang\":\"vi\"}', 'http://recland.local/job/api/list', 'http://recland.local/', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"24\"],\"x-xsrf-token\":[\"eyJpdiI6IklqYzRBSWQyVTVlSkRLcS95aFFpV3c9PSIsInZhbHVlIjoiT0dQdktpV0Q5S0swVkRRd04yc0YyVUd5aEdjNUozbk5nTjgyY3NaVzQ0bEY2eDNSUG1FRHdZbldNVWp6VXlURkpweDBDOXczalV2dDFaUmhlU05TWkNJSU9hQktaVkw4SkY4UXdHLytVSEsyN3JFdGlKTnlweWZUN3E5SWxGRzUiLCJtYWMiOiJiOTQzZWFiNWM3NTdkNzVhOTM1MWNmMmUyNzY5MDMwZDczMTcxMzM3NWE3NTY0ZDY3ZDY4YzMxYmE2MTc5Mjk3IiwidGFnIjoiIn0=\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/plain, *\\/*\"],\"content-type\":[\"application\\/json\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IklqYzRBSWQyVTVlSkRLcS95aFFpV3c9PSIsInZhbHVlIjoiT0dQdktpV0Q5S0swVkRRd04yc0YyVUd5aEdjNUozbk5nTjgyY3NaVzQ0bEY2eDNSUG1FRHdZbldNVWp6VXlURkpweDBDOXczalV2dDFaUmhlU05TWkNJSU9hQktaVkw4SkY4UXdHLytVSEsyN3JFdGlKTnlweWZUN3E5SWxGRzUiLCJtYWMiOiJiOTQzZWFiNWM3NTdkNzVhOTM1MWNmMmUyNzY5MDMwZDczMTcxMzM3NWE3NTY0ZDY3ZDY4YzMxYmE2MTc5Mjk3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImE2U0xTQkxJMWFSWGg2U3hnSFhuZlE9PSIsInZhbHVlIjoicmI5RGtBRWpJYW1aTFJpNnFKYkJhZXlBWXRpbEM2QzJaaVF6NzJKZGZ4MkdpcVhEZjZjdUJKM1ZST1Y4bVVnRklPUHBEQnFubCt4VjdabVJTcnlQcXE5amYrMyt5eWVteUZFOTRYVmlTNHZFWlVQV0xzKzBKWFkzSUFzKzBlZ2giLCJtYWMiOiI2MTdmMmY0OWQ0NzBjMDY4NjU4ZmM3NDljYzZmMzEzMmM4M2M4MTlhNGE1ZjU0ZjA5OGIwZjllNDFkODkyZDNiIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 13:59:35', '2025-08-07 13:59:35')
-- ","Time:":2.33} 
[2025-08-07 13:59:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select `job`.*, `job_meta`.`priority` from `job` inner join `job_meta` on `job`.`id` = `job_meta`.`job_id` where `job`.`is_active` = '1' and `job`.`status` = '1' and `job`.`expire_at` >= '2025-08-07' order by FIELD(job_meta.priority, 'Premium', 'Hot', 'New'), `job`.`publish_at` desc limit 30
-- ","Time:":56.58} 
[2025-08-07 13:59:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` in (3, 187, 217, 274, 277, 278, 286, 487, 491, 694, 695, 696)
-- ","Time:":0.95} 
[2025-08-07 13:59:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` where `job_meta`.`job_id` in (631, 700, 821, 825, 826, 827, 828, 838, 861, 865, 866, 1625, 1626, 1628, 1629, 1630, 1631, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644)
-- ","Time:":1.33} 
[2025-08-07 13:59:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select skills from `job` where `skills` is not null and `skills` != '' and `is_active` = '1' and `status` = '1' and `expire_at` >= '2025-08-07' group by `skills` order by COUNT(id) DESC limit 8
-- ","Time:":20.18} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1644' and `type` = 'save'
-- ","Time:":0.65} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1642' and `type` = 'save'
-- ","Time:":0.34} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1643' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '631' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1641' and `type` = 'save'
-- ","Time:":0.36} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1640' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1628' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1629' and `type` = 'save'
-- ","Time:":0.31} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1630' and `type` = 'save'
-- ","Time:":0.35} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1625' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1626' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1631' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1632' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1633' and `type` = 'save'
-- ","Time:":0.23} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1634' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1635' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1636' and `type` = 'save'
-- ","Time:":0.44} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1637' and `type` = 'save'
-- ","Time:":0.24} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1638' and `type` = 'save'
-- ","Time:":0.5} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1639' and `type` = 'save'
-- ","Time:":0.34} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '700' and `type` = 'save'
-- ","Time:":0.34} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '821' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '865' and `type` = 'save'
-- ","Time:":0.36} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '866' and `type` = 'save'
-- ","Time:":0.36} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":26.87} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '861' and `type` = 'save'
-- ","Time:":0.23} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '838' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '825' and `type` = 'save'
-- ","Time:":0.23} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '826' and `type` = 'save'
-- ","Time:":0.36} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '827' and `type` = 'save'
-- ","Time:":0.37} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '828' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer', 'http://recland.local/', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IklqYzRBSWQyVTVlSkRLcS95aFFpV3c9PSIsInZhbHVlIjoiT0dQdktpV0Q5S0swVkRRd04yc0YyVUd5aEdjNUozbk5nTjgyY3NaVzQ0bEY2eDNSUG1FRHdZbldNVWp6VXlURkpweDBDOXczalV2dDFaUmhlU05TWkNJSU9hQktaVkw4SkY4UXdHLytVSEsyN3JFdGlKTnlweWZUN3E5SWxGRzUiLCJtYWMiOiJiOTQzZWFiNWM3NTdkNzVhOTM1MWNmMmUyNzY5MDMwZDczMTcxMzM3NWE3NTY0ZDY3ZDY4YzMxYmE2MTc5Mjk3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImE2U0xTQkxJMWFSWGg2U3hnSFhuZlE9PSIsInZhbHVlIjoicmI5RGtBRWpJYW1aTFJpNnFKYkJhZXlBWXRpbEM2QzJaaVF6NzJKZGZ4MkdpcVhEZjZjdUJKM1ZST1Y4bVVnRklPUHBEQnFubCt4VjdabVJTcnlQcXE5amYrMyt5eWVteUZFOTRYVmlTNHZFWlVQV0xzKzBKWFkzSUFzKzBlZ2giLCJtYWMiOiI2MTdmMmY0OWQ0NzBjMDY4NjU4ZmM3NDljYzZmMzEzMmM4M2M4MTlhNGE1ZjU0ZjA5OGIwZjllNDFkODkyZDNiIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 13:59:36', '2025-08-07 13:59:36')
-- ","Time:":0.82} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.55} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.34} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.11} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.32} 
[2025-08-07 13:59:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.58} 
[2025-08-07 13:59:38] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"action\":\"register\"}', 'http://recland.local/employer/register?action=register', 'http://recland.local/employer', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlJMWTNuQnpYbHN5OVRsblJQb2FoTVE9PSIsInZhbHVlIjoibm9OSjZTNTJFNVg2c2xid1p4VEFzUTVzWGE2Y0V5NjBSOGdJR3FkQ0p4V3RqeGwyN2t6bit6R1JJbWlCczJkMnF1ZzVmRXdqN29FMzdjT0ppcmt5bzBmZTJsTHVTL1c5MW9DVHB0N0R4Q05MMGtCWkdyT3dWRWtnMnBtNmlEM1oiLCJtYWMiOiJmYzg3ZGUwNjc5NTkxNzc4ZGFiZDNhYjMyNTUzZDA0OWZhNGQwNjM4ZTk5ZDM3OTRhNjBmZGVlZDlkY2RlYjViIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkJwTG1JcEYrRTFwSzFmNGJTZ294U3c9PSIsInZhbHVlIjoiS2NrSTVyQXUvSVMzeW9ENU1xb1QxYWt5Mmh0V2ZNNytqei9tdXpZZVdTaWxYUTRsOGp0eGlPcmZWK2V4QlVPU3ZvWnp2L2JYYjBydnJlM3lVd2FCb25pMTFGdVR4V1BsUSs0VVRBVnJTUHlvTVpDckJKT0lhTWJwREVoYXMvdVEiLCJtYWMiOiI5ODc3ZGE4ODk0MGMzNDVlOWQyNDI5YzIwNjA4N2VjNjhhZDZlYWFhMDc1NTJhYTJlZDY2M2YxOWYxZTNlNDJmIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 13:59:38', '2025-08-07 13:59:38')
-- ","Time:":21.38} 
[2025-08-07 13:59:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-register' limit 1
-- ","Time:":0.74} 
[2025-08-07 13:59:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.48} 
[2025-08-07 13:59:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.62} 
[2025-08-07 13:59:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.5} 
[2025-08-07 13:59:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.43} 
[2025-08-07 13:59:43] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"action\":\"register\"}', 'http://recland.local/employer/register?action=register', 'http://recland.local/employer', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjdEN3ptZ3llL3FOWGQvUFRtVzJpNkE9PSIsInZhbHVlIjoiSXRRcnRIT20rTDU5OGVxdkdZZHFlSzduR3RIY3pjcmpmUDdSbC9JNGhZUHVJRmZlRytJK0NTOVRPaUt2YlcyRk1UOTFqOW93VjdEcmZBdzRhRkdEaTc4d0NsMlJmdnJOUTA3emUyMGdxNGxoa1BsMHFCQXlWRSs0cUt0ZGZsd3MiLCJtYWMiOiJiMjQ4ZTk5MTZmMjg5ODhmMjQ5MzFhNmZlYTJjMTI5ZGFmMTJlZTcwNmJmYzg4YTQyNTc3YzE2NGI2OTFkNmFkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkdUWHREckgwYjdneVpVZlBMVTB6UEE9PSIsInZhbHVlIjoiTjF0MHJoejNNejAyalVYOXl6ZERYQlJYMW00OElESC9INzZYUW1vMThwVmh6cWUwVHZwbEtpZnpJdVRxcEpUT0RXSnd4UytpRFgzM3RWRGtjR3RQS0xTTkpDT3JhWHJzZ2hvcUFhVWxXN255K09QeHI2cmdoNFVqQ3RZN0FUMmkiLCJtYWMiOiI2MDQ3NDcyMmMxMTdkNWE0ZGQ1OTJhN2U1ZjZiNWRkNjBjMzYxOTk1ODE0NjUwMDI4ODYwZDMzYTA2M2NjZjlhIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 13:59:43', '2025-08-07 13:59:43')
-- ","Time:":19.7} 
[2025-08-07 13:59:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-register' limit 1
-- ","Time:":0.63} 
[2025-08-07 13:59:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.34} 
[2025-08-07 13:59:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.11} 
[2025-08-07 13:59:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.36} 
[2025-08-07 13:59:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.31} 
[2025-08-07 13:59:52] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"name\":\"Admin Hri\",\"mobile\":\"0932329007\",\"email\":\"<EMAIL>\",\"company_name\":\"HRI\",\"work_position\":\"dev\",\"work_location\":\"hn\",\"password\":\"111111\",\"confirm_password\":\"111111\",\"accept_terms\":\"on\"}', 'http://recland.local/employer/register', 'http://recland.local/employer/register?action=register', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"217\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/register?action=register\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjFxU0lOOG5PTkZnMHlVUll6MGw5ZWc9PSIsInZhbHVlIjoidkxQR21LUGVRaUp1M1p0RWRSYi9XTW1vUnI5MWtIWCt0Rk9ZYW1Yb2hmUnlMRVVVTExTZnVyd0REdDlhQWFPeGhQQTc0eHRvK0pESXgzNy8yMEg5cXBZOEVJYWtMZWdHdW1acVBvWXZ0RTQ0cjVPZjIyK0hlTXFkRHhXVXdKN2giLCJtYWMiOiJiYThlNTk5OTI2MDk1MzQ3ZDY1MzRmZGJkMjU0ZDBkNDU4NzU5ZTY3OTgwMGY3ZTM0YWY0YmIwNDliYTU5YjFjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im84Ujc3YTdndkxSN3BITEVMNnhqN1E9PSIsInZhbHVlIjoicG8xZ284ZGYxY0l3VGlKWUdHQ1Q3UkVFeC81YzlwdW5neTQzUlJhREVwVXZKcG5EMXMzeWtoUHVGSlRFY1ZJQ2RoQnBPTzZXY252d1hBVmRIbXB6Mm96cE1jRkhndGNIWFBwczNUQ2xlRmxaekRqcmdiSXg4eHdCS2tqVWhDOFUiLCJtYWMiOiIwYzZhNTY4OWMxZjU0NGVkMGEwNmU0MjNiMjVjYmI0ZGU4NjJmNmNmYTZmZTYzZTQyMzExMDM4NzNmMGIyNTcwIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 13:59:52', '2025-08-07 13:59:52')
-- ","Time:":4.72} 
[2025-08-07 13:59:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1
-- ","Time:":1.85} 
[2025-08-07 13:59:53] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `users` (`name`, `email`, `mobile`, `company_name`, `mst`, `work_position`, `address`, `work_location`, `referral_code`, `password`, `type`, `token`, `last_login_at`, `updated_at`, `created_at`) values ('Admin Hri', '<EMAIL>', '0932329007', 'HRI', '', 'dev', 'hn', 'hn', '', '$2y$10$NE6FG7e6qhdajFzLZt..xuaqjn8iGbrwXLA4fMo3HU5KaxnSn2Ip.', 'employer', 'xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I', '2025-08-07 13:59:53', '2025-08-07 13:59:53', '2025-08-07 13:59:53')
-- ","Time:":0.79} 
[2025-08-07 13:59:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.91} 
[2025-08-07 13:59:53] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('[]', '{\"name\":\"Admin Hri\",\"email\":\"<EMAIL>\",\"mobile\":\"0932329007\",\"company_name\":\"HRI\",\"mst\":null,\"work_position\":\"dev\",\"address\":\"hn\",\"work_location\":\"hn\",\"referral_code\":\"\",\"password\":\"$2y$10$NE6FG7e6qhdajFzLZt..xuaqjn8iGbrwXLA4fMo3HU5KaxnSn2Ip.\",\"type\":\"employer\",\"token\":\"xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I\",\"last_login_at\":\"2025-08-07 13:59:53\",\"id\":7488}', 'created', '7488', 'App\\Models\\User', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/employer/register', '2025-08-07 13:59:53', '2025-08-07 13:59:53')
-- ","Time:":1.94} 
[2025-08-07 13:59:54] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `employer_types` (`user_id`, `type`, `updated_at`, `created_at`) values ('7488', 'manager', '2025-08-07 13:59:54', '2025-08-07 13:59:54')
-- ","Time:":0.55} 
[2025-08-07 13:59:54] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `wallets` (`user_id`, `type`, `updated_at`, `created_at`) values ('7488', 'employer', '2025-08-07 13:59:54', '2025-08-07 13:59:54')
-- ","Time:":0.52} 
[2025-08-07 13:59:54] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('[]', '{\"user_id\":7488,\"type\":\"employer\",\"id\":5804}', 'created', '5804', 'App\\Models\\Wallet', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/employer/register', '2025-08-07 13:59:54', '2025-08-07 13:59:54')
-- ","Time:":0.42} 
[2025-08-07 13:59:56] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[RecLand] [THÔNG BÁO NHÀ TUYỂN DỤNG MỚI]', '<!DOCTYPE html>

<html xmlns=\"http://www.w3.org/1999/xhtml\"

      class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface no-generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths js csstransforms csstransforms3d csstransitions responsejs \">

<meta charset=\"UTF-8\"/>

<head>

    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/>

    <style type=\"text/css\" media=\"only screen and (max-width: 480px)\">

        /* Mobile styles */

        @media only screen and (max-width: 480px) {

            [class=\"w320\"] {

                width: 320px !important;

            }



            [class=\"mobile-block\"] {

                width: 100% !important;

                display: block !important;

            }

        }

    </style>

</head>

<body style=\"margin: 0; cursor: auto; overflow: visible;\" class=\"ui-sortable\" data-new-gr-c-s-check-loaded=\"14.981.0\">

<div align=\"center\" style=\"background-color: #e5e5e5;\">

    <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 600px\">

        <tr>

            <td>

                <div align=\"center\">































                    <a href=\"http://recland.local\" target=\"_blank\"

                       style=\"border-color: transparent; border-width: 20px 20px 10px 20px; border-style: solid; display: inline-block;\">

                        <img width=\"140\" alt=\"\" src=\"https://i.imgur.com/9aOUeZI.png\"/>

                    </a>

                </div>

                <div style=\"background-color: #fff\">

                    <p style=\"width: 100%; background: linear-gradient(90.01deg, #1C4355 -0.66%, #EA6D56 104.24%); height: 8px\">

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; margin-top: 25px; border-width: 0 40px; border-style: solid; border-color: #fff\">

                        Name: <b>Admin Hri</b>,

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Email: <EMAIL>

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Mobile: 0932329007

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Company: HRI

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Nguồn: 

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Vị trí công tác: dev

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Khu vực: hn

                    </p>



                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Regards,</p>



                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        RECLAND</p>



                    <hr style=\"border-width: 0; border-bottom: 1px solid #EDEDED; margin: 0\"/>

                </div>

            </td>

        </tr>

    </table>

</div>

</body>

</html>



', '86b1a728a6c332099b08ee07058a9b65', '0', '2025-08-07 13:59:56', '2025-08-07 13:59:56')
-- ","Time:":1.55} 
[2025-08-07 13:59:58] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\"uuid\":\"423954a0-a512-4876-a3b2-5401fdf0dc6f\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:4525:\\\"<!DOCTYPE html>\\r\\n<html xmlns=\\\"http:\\/\\/www.w3.org\\/1999\\/xhtml\\\"\\r\\n      class=\\\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface no-generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths js csstransforms csstransforms3d csstransitions responsejs \\\">\\r\\n<meta charset=\\\"UTF-8\\\"\\/>\\r\\n<head>\\r\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\"\\/>\\r\\n    <style type=\\\"text\\/css\\\" media=\\\"only screen and (max-width: 480px)\\\">\\r\\n        \\/* Mobile styles *\\/\\r\\n        @media only screen and (max-width: 480px) {\\r\\n            [class=\\\"w320\\\"] {\\r\\n                width: 320px !important;\\r\\n            }\\r\\n\\r\\n            [class=\\\"mobile-block\\\"] {\\r\\n                width: 100% !important;\\r\\n                display: block !important;\\r\\n            }\\r\\n        }\\r\\n    <\\/style>\\r\\n<\\/head>\\r\\n<body style=\\\"margin: 0; cursor: auto; overflow: visible;\\\" class=\\\"ui-sortable\\\" data-new-gr-c-s-check-loaded=\\\"14.981.0\\\">\\r\\n<div align=\\\"center\\\" style=\\\"background-color: #e5e5e5;\\\">\\r\\n    <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"min-width: 600px\\\">\\r\\n        <tr>\\r\\n            <td>\\r\\n                <div align=\\\"center\\\">\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n                    <a href=\\\"http:\\/\\/recland.local\\\" target=\\\"_blank\\\"\\r\\n                       style=\\\"border-color: transparent; border-width: 20px 20px 10px 20px; border-style: solid; display: inline-block;\\\">\\r\\n                        <img width=\\\"140\\\" alt=\\\"\\\" src=\\\"https:\\/\\/i.imgur.com\\/9aOUeZI.png\\\"\\/>\\r\\n                    <\\/a>\\r\\n                <\\/div>\\r\\n                <div style=\\\"background-color: #fff\\\">\\r\\n                    <p style=\\\"width: 100%; background: linear-gradient(90.01deg, #1C4355 -0.66%, #EA6D56 104.24%); height: 8px\\\">\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; margin-top: 25px; border-width: 0 40px; border-style: solid; border-color: #fff\\\">\\r\\n                        Name: <b>Admin Hri<\\/b>,\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Email: <EMAIL>\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Mobile: 0932329007\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Company: HRI\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Ngu\\u1ed3n: \\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        V\\u1ecb tr\\u00ed c\\u00f4ng t\\u00e1c: dev\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Khu v\\u1ef1c: hn\\r\\n                    <\\/p>\\r\\n\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Regards,<\\/p>\\r\\n\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        RECLAND<\\/p>\\r\\n\\r\\n                    <hr style=\\\"border-width: 0; border-bottom: 1px solid #EDEDED; margin: 0\\\"\\/>\\r\\n                <\\/div>\\r\\n            <\\/td>\\r\\n        <\\/tr>\\r\\n    <\\/table>\\r\\n<\\/div>\\r\\n<\\/body>\\r\\n<\\/html>\\r\\n\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:5:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:2:\\\"cc\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"Cc\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:49:\\\"[RecLand] [TH\\u00d4NG B\\u00c1O NH\\u00c0 TUY\\u1ec2N D\\u1ee4NG M\\u1edaI]\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:11:\\\"{\\\"Cc\\\":[{}]}\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}', '2025-08-07 13:59:58', '2025-08-07 13:59:58')
-- ","Time:":1.32} 
[2025-08-07 13:59:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` where `hash` = 'bc1df78e55cbcd85d3218d86aa7d619e' and `created_at` >= '2025-08-07 13:54:58'
-- ","Time:":266.67} 
[2025-08-07 13:59:59] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[]', '[Recland] XÁC THỰC TÀI KHOẢN NHÀ TUYỂN DỤNG', '<!DOCTYPE html>

<html>



<body width=\"100%\"

    style=\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\">

    <center style=\"width: 100%; background-color: #f1f1f1;\">

        <div style=\"max-width: 600px; margin: 0 auto;\">

            <table align=\"center\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\"

                style=\"margin: auto;\">

                <tr>

                    <td>

                        <div style=\"text-align: center;padding: 25px 0;background: #FCFCFE;\">

                            <img style=\"max-width: 100%\"

                                src=\"http://recland.local/frontend/assets_v2/images/graphics/logo-250.png?v=68944eef6a664\">

                        </div>

                    </td>

                </tr>

            </table>

            <div style=\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\">

                <div

                    style=\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;

                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=68944eef6a68e);

                background-repeat: no-repeat;background-size: 100%;\">

                    <table>

                        <tr>

                            <td>

                                <div

                                    style=\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\">

                                    <div style=\"margin-bottom: 14px\">

                                                                            </div>

                                    <div>

                                                                            </div>



                                </div>

                            </td>

                        </tr>

                    </table>

                    <table style=\"width: 100%\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"

                        width=\"100%\">

                        <tr>

                            <td>

                                    <div

        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">

        <p>Xin chào, Admin Hri</p>

        <p>Xin chúc mừng bạn đã tạo tài khoản Nhà Tuyển Dụng Recland thành công.</p>

        <p>Chào mừng bạn đến với Cộng đồng Recland - Nền tảng tuyển dụng trực tuyến hàng đầu, kết nối Nhà tuyển dụng với

            đội ngũ HR Freelancer trên toàn quốc.</p>

                    <p style=\"margin-bottom: 24px\">Hãy xác nhận rằng bạn đang sử dụng email này cho tài khoản Recland của mình để

                làm tài khoản đăng nhập. Chúng tôi sẽ gửi những thông báo quan trọng đến bạn.</p>

            <p style=\"margin-bottom: 24px\">Xác nhận địa chỉ email để nhận ngay <strong>200.000đ</strong></p>

            <p style=\"text-align: center;margin-bottom: 0\">

                <a style=\"

            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);

            font-weight: 400;

            font-size: 16px;

            margin: 0 auto;

            color: #ffffff;

            display: block;

            width: 300px;

            height: 46px;

            line-height: 46px;

            border-radius: 8px;

            margin-bottom: 16px;

            text-decoration: none;

                \"

                    href=\"http://recland.local/verify-employer?token=xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I\">

                    Xác minh Email của Bạn

                </a>

            </p>

                

        <p><b>Trân trọng,</b></p>

        <p><i>Đội ngũ Recland.</i></p>

    </div>

    <div style=\"border: 5px solid #F7F7F7;\"></div>

    <div

        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">

        <p>Hello, Admin Hri</p>

        <p>Congratulations on successfully creating your employer account on Recland.</p>

        <p>Welcome to the Recland Community - The leading online Recruitment Platform that connects Employers with a

            nationwide network of freelance headhunters.</p>

                    <p style=\"margin-bottom: 24px\">Please confirm that you are using this email for your Recland account to

                make your login account. We will send important notifications to you.</p>

            <p style=\"margin-bottom: 24px\">Confirm your email address to receive <strong>200.000đ</strong></p>

            <p style=\"text-align: center;margin-bottom: 0\">

                <a style=\"

            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);

            font-weight: 400;

            font-size: 16px;

            margin: 0 auto;

            color: #ffffff;

            display: block;

            width: 300px;

            height: 46px;

            line-height: 46px;

            border-radius: 8px;

            margin-bottom: 16px;

            text-decoration: none;

                \"

                    href=\"http://recland.local/verify-employer?token=xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I\">

                    Verify Your Email

                </a>

            </p>

                

        <p><b>Thanks and best regards,</b></p>

        <p><i>Recland Team,</i></p>

    </div>

                            </td>

                        </tr>

                        <tr>

                            <td>

                                <div style=\"padding:12px 0\">

                                    <div

                                        style=\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=68944eef6a6da);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\">

                                        <div style=\"margin-bottom: 12px;text-align: center\">

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=68944eef6a6f8\"></a>

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=68944eef6a714\"></a>

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=68944eef6a72f\"></a>

                                        </div>

                                        <p

                                            style=\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\">

                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>

                                        <p

                                            style=\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\">

                                            © 2022 Recland.co</p>

                                    </div>

                                </div>



                            </td>

                        </tr>

                    </table>

                </div>

            </div>

        </div>

    </center>

</body>



</html>

', '85c1f4bbce744c6664324111faf7a37e', '0', '2025-08-07 13:59:59', '2025-08-07 13:59:59')
-- ","Time:":1.26} 
[2025-08-07 13:59:59] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\"uuid\":\"8ae66e3c-665f-4234-b840-9025a8088fce\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:8261:\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/assets_v2\\/images\\/graphics\\/logo-250.png?v=68944eef6a664\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=68944eef6a68e);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Xin ch\\u00e0o, Admin Hri<\\/p>\\r\\n        <p>Xin ch\\u00fac m\\u1eebng b\\u1ea1n \\u0111\\u00e3 t\\u1ea1o t\\u00e0i kho\\u1ea3n Nh\\u00e0 Tuy\\u1ec3n D\\u1ee5ng Recland th\\u00e0nh c\\u00f4ng.<\\/p>\\r\\n        <p>Ch\\u00e0o m\\u1eebng b\\u1ea1n \\u0111\\u1ebfn v\\u1edbi C\\u1ed9ng \\u0111\\u1ed3ng Recland - N\\u1ec1n t\\u1ea3ng tuy\\u1ec3n d\\u1ee5ng tr\\u1ef1c tuy\\u1ebfn h\\u00e0ng \\u0111\\u1ea7u, k\\u1ebft n\\u1ed1i Nh\\u00e0 tuy\\u1ec3n d\\u1ee5ng v\\u1edbi\\r\\n            \\u0111\\u1ed9i ng\\u0169 HR Freelancer tr\\u00ean to\\u00e0n qu\\u1ed1c.<\\/p>\\r\\n                    <p style=\\\"margin-bottom: 24px\\\">H\\u00e3y x\\u00e1c nh\\u1eadn r\\u1eb1ng b\\u1ea1n \\u0111ang s\\u1eed d\\u1ee5ng email n\\u00e0y cho t\\u00e0i kho\\u1ea3n Recland c\\u1ee7a m\\u00ecnh \\u0111\\u1ec3\\r\\n                l\\u00e0m t\\u00e0i kho\\u1ea3n \\u0111\\u0103ng nh\\u1eadp. Ch\\u00fang t\\u00f4i s\\u1ebd g\\u1eedi nh\\u1eefng th\\u00f4ng b\\u00e1o quan tr\\u1ecdng \\u0111\\u1ebfn b\\u1ea1n.<\\/p>\\r\\n            <p style=\\\"margin-bottom: 24px\\\">X\\u00e1c nh\\u1eadn \\u0111\\u1ecba ch\\u1ec9 email \\u0111\\u1ec3 nh\\u1eadn ngay <strong>200.000\\u0111<\\/strong><\\/p>\\r\\n            <p style=\\\"text-align: center;margin-bottom: 0\\\">\\r\\n                <a style=\\\"\\r\\n            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);\\r\\n            font-weight: 400;\\r\\n            font-size: 16px;\\r\\n            margin: 0 auto;\\r\\n            color: #ffffff;\\r\\n            display: block;\\r\\n            width: 300px;\\r\\n            height: 46px;\\r\\n            line-height: 46px;\\r\\n            border-radius: 8px;\\r\\n            margin-bottom: 16px;\\r\\n            text-decoration: none;\\r\\n                \\\"\\r\\n                    href=\\\"http:\\/\\/recland.local\\/verify-employer?token=xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I\\\">\\r\\n                    X\\u00e1c minh Email c\\u1ee7a B\\u1ea1n\\r\\n                <\\/a>\\r\\n            <\\/p>\\r\\n                \\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Hello, Admin Hri<\\/p>\\r\\n        <p>Congratulations on successfully creating your employer account on Recland.<\\/p>\\r\\n        <p>Welcome to the Recland Community - The leading online Recruitment Platform that connects Employers with a\\r\\n            nationwide network of freelance headhunters.<\\/p>\\r\\n                    <p style=\\\"margin-bottom: 24px\\\">Please confirm that you are using this email for your Recland account to\\r\\n                make your login account. We will send important notifications to you.<\\/p>\\r\\n            <p style=\\\"margin-bottom: 24px\\\">Confirm your email address to receive <strong>200.000\\u0111<\\/strong><\\/p>\\r\\n            <p style=\\\"text-align: center;margin-bottom: 0\\\">\\r\\n                <a style=\\\"\\r\\n            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);\\r\\n            font-weight: 400;\\r\\n            font-size: 16px;\\r\\n            margin: 0 auto;\\r\\n            color: #ffffff;\\r\\n            display: block;\\r\\n            width: 300px;\\r\\n            height: 46px;\\r\\n            line-height: 46px;\\r\\n            border-radius: 8px;\\r\\n            margin-bottom: 16px;\\r\\n            text-decoration: none;\\r\\n                \\\"\\r\\n                    href=\\\"http:\\/\\/recland.local\\/verify-employer?token=xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I\\\">\\r\\n                    Verify Your Email\\r\\n                <\\/a>\\r\\n            <\\/p>\\r\\n                \\r\\n        <p><b>Thanks and best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland Team,<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=68944eef6a6da);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=68944eef6a6f8\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=68944eef6a714\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=68944eef6a72f\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:4:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:54:\\\"[Recland] X\\u00c1C TH\\u1ef0C T\\u00c0I KHO\\u1ea2N NH\\u00c0 TUY\\u1ec2N D\\u1ee4NG\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:2:\\\"[]\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}', '2025-08-07 13:59:59', '2025-08-07 13:59:59')
-- ","Time:":3.56} 
[2025-08-07 13:59:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` where `hash` = 'bfa4a1d460b738c8ab8223478d4da73a' and `created_at` >= '2025-08-07 13:54:59'
-- ","Time:":175.97} 
[2025-08-07 14:00:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":15.65} 
[2025-08-07 14:00:00] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"from\":\"registed-success\"}', 'http://recland.local/employer?from=registed-success', 'http://recland.local/employer/register?action=register', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/register?action=register\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBzSklYczJtaUpFNlB6VVN1TDVrR1E9PSIsInZhbHVlIjoiRmlCTldIV2lIekNOaXhveWxITno0VEVkcXNERzN1eTNxOWxjVzVzZ0JnWW1ja1MwQ3JpVjdub3pzeG9tQStsZlNKRk0zWjJrNkZUTjlJWEk4QnEyT09DbElUd2dDUHI2OHpxVUh2ajh5ZXVXdU1WUml0STVvd3NHdEUzeTJkQWMiLCJtYWMiOiJmNGNiMGQ0MDgwNThlZmRkODhhZTNiMWZkOTFmZjg2ZTA5ZTNkYjc0ZTJlZDc0OTc2ODBhOGE2N2NhZTVkNGFlIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlUzV3lMK2hiT3l2emk0UVBoWHpJUmc9PSIsInZhbHVlIjoia2FsQ3JnRDVzRnVRa3JWa24wZHluT2VHKzhpVktOS2Z3ZmpFZFZDcE1IWjFPTzYzcWxTNFF2VGtrUWpxcGk1VU5aYTg5OHJxLzF4ekNtbXpSa0JzbFFDMzhZYXJHa0VLL3IwVjZsSmxhcytySEdGN0lxWGNBVVF5UmR5RGpYdFIiLCJtYWMiOiJmM2UzYTFiODA0NGMwNWFiMTZlYjIxYzAxYzIzMTlhODMxOWE3YjMwMTM4N2U2NDBhNjM1ZWU0OTgzNWZhOTkzIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:00:00', '2025-08-07 14:00:00')
-- ","Time:":0.65} 
[2025-08-07 14:00:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.55} 
[2025-08-07 14:00:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.49} 
[2025-08-07 14:00:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.56} 
[2025-08-07 14:00:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":1.07} 
[2025-08-07 14:00:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.53} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"token\":\"xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I\"}', 'http://recland.local/verify-employer?token=xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I', '', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik4yeUJGNXo5cUVKaDBlc3I4MWlqU1E9PSIsInZhbHVlIjoiT0k2blk2QVBGbmlGd0FCdXp1aktaSzkwbk9nUmxla1h1NCtqRnpJWjNNdWlTb1FqZVBYUFIvOHZPOEdWQ0NNTDhqc3lER1I5V1ZTYUxxNlNhSDMrZ0p6TWFqWlVLcktkWEZ5NGxkZTVVYTR0dm1TWjQ3aWNLUlJCRmtOcER0SEEiLCJtYWMiOiI0NmU3ZTBhOWVjNDRlZDI3YTEwY2Q1MDgzZmJjMzkwMGIyZWNkNzljN2ZlODRhYzI3MDM4ODlmNGU0NDExOTJlIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik5maTdXN2V5MjY5UmtJMUhpU3hmWGc9PSIsInZhbHVlIjoiaFcwTVc1aEY5U01ucnJ0TFBEMkJXSkdtT0J2WUFVM1RTNG11WEd6UElPUzVnTmpVMGg5NFoxSUJQd1EwWVpaVDlMcFY1NERmbWtpYU5RN2M2dFpoanE2dEdJQ0FicmZhMHV2YzVzSUpNOGZkRmhPTnBDaVMzbzBrL1lFMnJFY08iLCJtYWMiOiI1YTlhMWEwNzM4MjljNGI2NDc0NGYyZmJlNjVmNGFlMWQwNTFkNGE4MjM0Y2Y0ZDBlY2U1Y2YzNmRlNGY5YzZkIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:00:17', '2025-08-07 14:00:17')
-- ","Time:":19.2} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `token` = 'xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I' limit 1
-- ","Time:":22.22} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `users`.`id` = '7488' limit 1
-- ","Time:":0.44} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
update `users` set `email_verified_at` = '2025-08-07 14:00:17', `token` = '', `users`.`updated_at` = '2025-08-07 14:00:17' where `id` = '7488'
-- ","Time:":0.65} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.36} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\"email_verified_at\":null,\"token\":\"xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I\"}', '{\"email_verified_at\":\"2025-08-07 14:00:17\",\"token\":null}', 'updated', '7488', 'App\\Models\\User', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/verify-employer?token=xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I', '2025-08-07 14:00:17', '2025-08-07 14:00:17')
-- ","Time:":1.13} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":0.61} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1
-- ","Time:":0.83} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta` where `meta`.`owner_type` = 'App\\Models\\User' and `meta`.`owner_id` = '7488' and `meta`.`owner_id` is not null
-- ","Time:":15.99} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1
-- ","Time:":0.56} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `user_id` = '7488' limit 1
-- ","Time:":4.22} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
update `wallets` set `amount` = '200000', `wallets`.`updated_at` = '2025-08-07 14:00:17' where `id` = '5804'
-- ","Time:":0.66} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\"amount\":0}', '{\"amount\":200000}', 'updated', '5804', 'App\\Models\\Wallet', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/verify-employer?token=xWWWQKLT7njj8b4wmXEFZBZhpbMKJn5I', '2025-08-07 14:00:17', '2025-08-07 14:00:17')
-- ","Time:":0.7} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `wallet_transactions` (`wallet_id`, `amount`, `balance_after`, `note`, `type`, `created_at`, `updated_at`) values ('5804', '200000', '200000', 'Nạp tiền từ việc giới thiệu', 'employer_bonus_amount_refer', '2025-08-07 14:00:17', '2025-08-07 14:00:17')
-- ","Time:":2.05} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta` where `meta`.`owner_type` = 'App\\Models\\User' and `meta`.`owner_id` = '7488' and `meta`.`owner_id` is not null
-- ","Time:":7.3} 
[2025-08-07 14:00:17] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `meta` (`status`, `type`, `key`, `value`, `owner_id`, `owner_type`, `updated_at`, `created_at`) values ('1', 'integer', 'employer_bonus_amount_refer', '200000', '7488', 'App\\Models\\User', '2025-08-07 14:00:17', '2025-08-07 14:00:17')
-- ","Time:":1.26} 
[2025-08-07 14:00:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'market-cv' limit 1
-- ","Time:":28.05} 
[2025-08-07 14:00:18] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"from\":\"verify-email\"}', 'http://recland.local/employer/market?from=verify-email', '', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ilc5ZjBvR3RubURLU0lYaHFTS05sUnc9PSIsInZhbHVlIjoibktxQis2cmVrZ0w4MHArcVBsNjM4KzdqZ2dYN0kxUVhMUkNlMWR4QmYvY1N3NThVTWx5anE1TGFsUlNUdzlaNTlkSEdRank1NXoyTVFNS016Nk53dWt0bFJhMlNuM0hGZkRaYnJMRVl1M29vVDBHdFk2YlViRENETDBBaC9WT00iLCJtYWMiOiI4YjJmNzVhNjcxMTVjOWYzMzdlYmEwYWJkNGI5ODM5NmQwNDEwMjM2YzA0MDU1OGM1NDIwOWEwYTg3MTMwMzZiIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkxkWmlmTCs0YXVja3Qxayt2ZDBWN0E9PSIsInZhbHVlIjoiK29GWWV3S2JVaVBpSk5HYUpvNk1va1dhQ0NML1ZscVVvdWllZWF1cjJ1bTdKWmxHYS9OWDlVRlNPbmk5ZHhKMXJYSlg5eDlIN250cnE0aU9DSW13akNEb1NWVkFXclFnZkhJdVFrL1FHUngwV2phaCsxc29zQUpVbUdVOVB0azIiLCJtYWMiOiI2NjRmMzgwZDc4ZTA5Njg3ZTVmM2UxYzMwODk0NTk0MjE3N2RiMGMzOGQxZTU2YjhiOWRmZDc1ZjY5ZGVmNGZjIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:00:18', '2025-08-07 14:00:18')
-- ","Time:":0.55} 
[2025-08-07 14:00:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.86} 
[2025-08-07 14:00:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":0.59} 
[2025-08-07 14:00:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `warehouse_cv_sellings` inner join 

                (select warehouse_cv_sellings.id, max(warehouse_cv_sellings.id) as max_id from warehouse_cv_sellings

                inner join warehouse_cvs on warehouse_cvs.id = warehouse_cv_sellings.warehouse_cv_id

                group by warehouse_cvs.candidate_mobile, type_of_sale) as t1

             on `warehouse_cv_sellings`.`id` = `t1`.`max_id` where exists (select * from `warehouse_cvs` where `warehouse_cv_sellings`.`warehouse_cv_id` = `warehouse_cvs`.`id` and `cv_private` is not null) and `warehouse_cv_sellings`.`status` = '0' and `warehouse_cv_sellings`.`deleted_at` is null
-- ","Time:":3386.76} 
[2025-08-07 14:00:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select `warehouse_cv_sellings`.* from `warehouse_cv_sellings` inner join 

                (select warehouse_cv_sellings.id, max(warehouse_cv_sellings.id) as max_id from warehouse_cv_sellings

                inner join warehouse_cvs on warehouse_cvs.id = warehouse_cv_sellings.warehouse_cv_id

                group by warehouse_cvs.candidate_mobile, type_of_sale) as t1

             on `warehouse_cv_sellings`.`id` = `t1`.`max_id` where exists (select * from `warehouse_cvs` where `warehouse_cv_sellings`.`warehouse_cv_id` = `warehouse_cvs`.`id` and `cv_private` is not null) and `warehouse_cv_sellings`.`status` = '0' and `warehouse_cv_sellings`.`deleted_at` is null order by `updated_at` desc limit 10 offset 0
-- ","Time:":3617.6} 
[2025-08-07 14:00:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` where `warehouse_cvs`.`id` in (87560, 87563, 87564, 87565, 87567, 87568, 87570, 87573, 87574, 87576)
-- ","Time:":1.04} 
[2025-08-07 14:00:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `user_id` = '7488' and `warehouse_cv_selling_buys`.`warehouse_cv_selling_id` in (79010, 79011, 79012, 79013, 79014, 79015, 79016, 79017, 79018, 79020) and `warehouse_cv_selling_buys`.`deleted_at` is null
-- ","Time:":0.69} 
[2025-08-07 14:00:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select max(`candidate_salary_expect`) as aggregate from `warehouse_cv_sellings` where `status` = '0' and `warehouse_cv_sellings`.`deleted_at` is null
-- ","Time:":38.66} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79020' limit 1
-- ","Time:":0.88} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79018' limit 1
-- ","Time:":0.66} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79017' limit 1
-- ","Time:":0.41} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79016' limit 1
-- ","Time:":0.38} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79015' limit 1
-- ","Time:":0.39} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79014' limit 1
-- ","Time:":0.41} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79013' limit 1
-- ","Time:":0.37} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79012' limit 1
-- ","Time:":0.33} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79011' limit 1
-- ","Time:":0.26} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` where `warehouse_cv_selling_saves`.`warehouse_cv_selling_id` = '79010' limit 1
-- ","Time:":0.26} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7488' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.58} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7488' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.71} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7488' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.36} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.85} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.41} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.42} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.4} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.39} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.39} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.39} 
[2025-08-07 14:00:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.39} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":20.03} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/market?from=verify-email', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/market?from=verify-email\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjQ0QUFzd1E5N1dOdW9mT2FFYjJuMGc9PSIsInZhbHVlIjoib09qL3JYeU45R3RZa21PRjd3SHhkOWNOa2xCMlN1NDE4MS9rcEtya2MxSnZSNnhnMDBMcUgzcnpBZWJnOUZPd21KV2R1YnZwcUR2Q1ZsRmNMYWFGWkhENmNONHNCYVU1UW01S213elZiemRTYWFjNU1YSW5xcU1IMVdqWXlGU0siLCJtYWMiOiJiZWNlYWFlNzEyYWI5OTdkNDZjZjNiMmUzYTRlM2YxYjE3OTIxNDQzZmNlNzFkZmQyZGM1Y2JmNTkwNjIzMjlmIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkUxRlAwREdBZlVtR0syVG51TlEvWlE9PSIsInZhbHVlIjoic1p5M0JoWStWb2xMZTlIY0VKNkZhdUFBVEVLYWgybEtRZkpVcU1HTUcxVkZQWTczSWswQ3IvbGVZT204cFFPbjJYTHBXZ3FpSEMrMDBUR0dwWm9LUzZodTJMS0tIRWJtZW40ZnE2a1lQMTlDcDVmWHlrY3hnbTYrTTRFQTBOa3oiLCJtYWMiOiJmY2QwNjBkNTJhZmM2ZDMyZjc2MDYzM2RiODg3ZmIwNzJhYWM0MzEyYmY0MWRiZWYyNTIwNTI0NjkxOTI5NTVjIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:01:05', '2025-08-07 14:01:05')
-- ","Time:":0.57} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":0.67} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.79} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.34} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7488' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.28} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.49} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":9.39} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.5} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.49} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.47} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.46} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.44} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.51} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.45} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7488' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.5} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7488' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.44} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7488' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.37} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.47} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.79} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.75} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.71} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.71} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.7} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.71} 
[2025-08-07 14:01:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.65} 
[2025-08-07 14:01:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'frontend.auth.employer.confirm' limit 1
-- ","Time:":14.45} 
[2025-08-07 14:01:09] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"confirm\":\"1\",\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\"}', 'http://recland.local/employer/users/confirm-policy', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"57\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjZncWxaUmk2bmlaeUhFSjlWY3ZORkE9PSIsInZhbHVlIjoiSzJZb1ErMkFTU01ZL3FrYnp4aVk0ZnV1blh0VkZBbHQ5RTZWMzB5VnhmQjh6Rys4Tmh3bHZiRlpQT1hWdU1LeFZwL0lmMVdsYldJT0NreU5aTnB6REZucXF3Mmt0QTVpS3kwMVlwT2tOTWhUTkJQR1pOT08rQU0raE5jV2lIMkgiLCJtYWMiOiJhYTFkMmFjYjUyNTU0YTYyNTM5NmMyZjFhZTkwM2Q3MzhhNzBhNDc4ZDI0ZmViNmQzNjc0NzFmN2Y1NTJjY2ZkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Iko0L0FnNnNFT3JQeW5CTzAxOENNYmc9PSIsInZhbHVlIjoiZ3RDQjRvZzlPUHFQWjdvVTFja0ZiUU9SK2VxY3pnL1MvUEJiUXUwOWEyRjVVMTZOcWcydlAwMnVrbzEyVWk4U1hQWnY2S2VwYnhTQ1FjUklFTFRlOUR2bDJ5NCtEM2NkNXV6dzc4T2FjeDBINjVBOWphdFUwRWFpSTlKZnZXMnciLCJtYWMiOiIzM2I2OTBlNGY1MDllZDUyOTY0YTMzYjg3ODMxZmJlYWE3YTYxYjdjNzRkNDZhOThmNWJlMWQxMWFiMTk2Y2IyIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:01:09', '2025-08-07 14:01:09')
-- ","Time:":0.35} 
[2025-08-07 14:01:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":0.37} 
[2025-08-07 14:01:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.41} 
[2025-08-07 14:01:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.23} 
[2025-08-07 14:01:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7488' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.27} 
[2025-08-07 14:01:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7488' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.39} 
[2025-08-07 14:01:09] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `meta_data` (`key`, `value`, `object_id`, `object_type`, `updated_at`, `created_at`) values ('employer_confirmed_at', '2025-08-07 14:01:09', '7488', 'App\\Models\\User', '2025-08-07 14:01:09', '2025-08-07 14:01:09')
-- ","Time:":9.03} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":17.78} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/market?from=verify-email', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/market?from=verify-email\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IksrM09QU3d0YXllSjY2bjBsOFJaTVE9PSIsInZhbHVlIjoiWHQrRk13OHNTQnVhWCtoZ0k5cnhjQjZIWHlUMzZRaGtZRU82cHNBellSaFlsSXIxdS9lQVdqRjhTUmJiQXFMcEd3dHpBdUNBenQ0VnV5VTI2THhQU0JMcnA4ckx4UCtTOXZGV2FFUVA1aS9qeTlUY0lkOS9aK0JHRzlKM1pQVUciLCJtYWMiOiI5OTZmYjk3Y2Q3MmI2M2M3YmZmYWRjNmI5ODZiYjNkNGExNDJiZDZlY2M3MzY0ZGQ0ZWM2YzE2YTMyZTEyOWE4IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlhRMEJ1ZGRjYjZIOFRESmc1TGx6dFE9PSIsInZhbHVlIjoiYTJocFVXZUQ4cmhEOXBIaitpZHJvRW1mTzVSUGd4czU4b01wWTduTWw2Mmp2em1VQUNJaHBGUndrSjZueUQrRm9XaTlza1V1a1RNM3RKZ2JwMXJGUjFrR0FLbmJNNGlwUkNxem8zVTZmRjc1UDZnWGVrU3RPNkZZNnBGWTNPckgiLCJtYWMiOiJhOTQyN2JkYTgzZGNlOGFlYzFlZTZkZDg0YTJkZjM4ZjhkZGZiODI1MDI3ZjA4ZjNiODdmYTNjMTRmZWNiZDA4IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:01:11', '2025-08-07 14:01:11')
-- ","Time:":0.4} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":0.44} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.47} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.23} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7488' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.76} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.31} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.36} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.47} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.48} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.43} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.43} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.43} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.42} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.42} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7488' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.45} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7488' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.39} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7488' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.35} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.44} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.43} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.45} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.7} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.69} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.73} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.69} 
[2025-08-07 14:01:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.73} 
[2025-08-07 14:01:54] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"career\":\"1\"}', 'http://recland.local/ajax/get-skill-main-it?career=1', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijduc2dFUnFUcmlRSW5qelh5aitBNFE9PSIsInZhbHVlIjoiVE1zUmVhMU9jRHpqNStwb3NUU2t6Qm9qMStMd08vM1FaQkt6Z1VnNEtld2tXSlRGMjZRc3RKVGl3NE9RNmZvWTNTeFRQcmI1UGVXYVllc3Z0NW1ZeDRlcEFPd25SdS9FbjF2L3ROaE1IaGg3Tnp3U3F5bVhDTnBoVmovKzJ2b3EiLCJtYWMiOiIyNDAxMGM5YTgyM2Y0MWQxZGZhYjkyODBhMjE3ZjYxNDA0NjdjNzUwOTc4ZDE5MGE3MzgyZmUzNGRhNzc2Y2QxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkF1Z2Zua2plSjkrWDJsTU9GWGxhK0E9PSIsInZhbHVlIjoiNkZkOHhkY1QxeTRaUmQvL251QVM0UEwzTGhETzRHUHRwby9YWDZ0TVRNOElpZENJMDBEcWlPTUFOR0xhL1dCYTcvc1p6UUdxTmVQOWVJMGxwclVBcEpxeFN4TTBOWko5RExXeXhRWk1LdDBWMDRLWlJVcGs3OGhkL2VST2pHTjQiLCJtYWMiOiJhY2FmM2VmZWQyNjFlMTVjZTQ0OWNjZTA3ZDcxNzYwYjc4Y2EyMWNjOGUwZGRjM2E4YTVhOWM5MTdjODUyNjU5IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:01:54', '2025-08-07 14:01:54')
-- ","Time:":19.74} 
[2025-08-07 14:01:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops`
-- ","Time:":0.76} 
[2025-08-07 14:01:56] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":\"1\",\"is_it\":\"false\"}', 'http://recland.local/ajax/get-level?group=1&is_it=false', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjU2M2s1MERzWW5YeTJ6bHNQNDMwc3c9PSIsInZhbHVlIjoiejhsOG5vbXFaV0MxN2RwSUFsUGpZQXIwTmdza1lTRnVEWVpEYkRnZ3poSUdodTlGUXpOeUJmT2ZlZ1F3Ulh6QXpFQlcwb3VaKzNZbmxvRkRCUzQ2d0JuTEs2L1NaNEJka2V0TkZTSWZLNW9xOWRnOVJ0RkZPRlEyN1pMbVltYkoiLCJtYWMiOiJlOWM2NWRmOTY2NTgyMmUwY2YyZDRlNjczZTQyOWU4ODU4MWMyN2U5OWVlOGQyNWM2MjQ2MjA1MzZmZTQzMjgxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkNabEV0NlFXUGxUbUQxMDdTUk04c1E9PSIsInZhbHVlIjoiRnpuMjFsMndSakZMcG9ka3VmWmF0amdURmdMMm1JZmhiQnB5NGdHeHdQVjdLMVRQWnpBTFFiS1dLaExuYnNLYmtWeHNLQjBVKzR3K2RIK0lyZWxDS1FvZE9FUDdBSjllb01sZTgrTzg4b2VHK2duSkhKRllseHJJbFBaOWZ2cDAiLCJtYWMiOiJhNjlmNmYwYTI5MWRiNTNjY2EyMzY4YjYxYmVmMDg0ZDRkZmNiOWE0ZDIxNzE1YzRhMDZiMjRkY2M4OTliNDFkIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:01:56', '2025-08-07 14:01:56')
-- ","Time:":19.36} 
[2025-08-07 14:01:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` where `group` = '1'
-- ","Time:":0.76} 
[2025-08-07 14:02:03] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":null,\"is_it\":null,\"bonus_type\":\"onboard\",\"level\":\"2\",\"career\":\"1\",\"salary_min\":\"1000000\",\"salary_max\":\"2000000\",\"salary_currency\":\"VND\",\"skill_id\":\"4\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=onboard&career=1&group=&is_it=&level=2&salary_currency=VND&salary_max=2000000&salary_min=1000000&skill_id=4', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IitUNG1JaHQ0Q2Q0eXJXMFNDOHkwR2c9PSIsInZhbHVlIjoiNmc2cGIxNmErdUQvQWZqOGtNRmw5YWVEbjhMNlNmUjdOUnV1b284Ri8yR05jbU5HRnFNNERwMCthblN5ZVJ3UEN6R01Ha24wQVBGSjNaYU5QOUpyVnFtaThFeWk5YkFBdTd4bnJxTWwxWjdhMWNOZGFqZ0MwQytCS0JJV1VJb3EiLCJtYWMiOiJjMjMyODQ0MTllMjcwNjhiYjJjYWJjM2NmNWFiZWMzM2ExMTYzMzllZGYwYTY0ZGMxNzIxMzMxNjEzN2Q3NWZhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InJQazFyZEtQT1Q2ejM5TTFaN1hsTnc9PSIsInZhbHVlIjoieHdzdVlUbXpxWEJkdzBTOXhQUnB4cE1OS2lBYUJ2OUlEREJxNzFEY0ZWN0hkL3d2UWcrVlloNDdhV3NwWEdYVlp3R1NCNUJxaE1XT0gvb1Rucm9vSnBRKzd3OGVoZE5xRTNXWS83Y2sxdU1rb0hOYnJDb2lBMkI5UHBZdVBFU0YiLCJtYWMiOiIyMWY0YThmYjgzY2Q1Njk4OGYzODQ2YTA4Yzk4YzI5MzI4YzNkYjBmMjdhMDE4ZDIwMzFiODlkZTZiYTVjYmViIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:02:03', '2025-08-07 14:02:03')
-- ","Time:":20.06} 
[2025-08-07 14:02:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-store' limit 1
-- ","Time:":14.16} 
[2025-08-07 14:02:15] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"name\":\"Bui Thanh\",\"expire_at\":\"20\\/08\\/2025\",\"vacancies\":\"11\",\"career\":\"1\",\"skill\":\"4\",\"rank\":\"2\",\"salary_currency\":\"VND\",\"salary_min\":\"1000000\",\"salary_currency_max\":\"VND\",\"salary_max\":\"2000000\",\"bonus_type\":\"onboard\",\"bonus\":\"2000000\",\"type\":\"full-time\",\"address\":[{\"area\":\"ho-chi-minh\",\"address\":\"hn\"},null],\"jd_description\":\"<p>hn<\\/p>\",\"jd_request\":\"<p>hn<\\/p>\",\"jd_welfare\":\"<p>hn<\\/p>\"}', 'http://recland.local/employer/job/store', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"2125\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"multipart\\/form-data; boundary=----WebKitFormBoundary7LRf51TmHHK1PMMB\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InA4QzgyREpHSXlFOFp5VU15NUNvYmc9PSIsInZhbHVlIjoiYkU0VlRrNkR6YXovem5Hbkk4YlFlSlhUZnUrZHY3aGVPdE9hUU1JTG81V3dnU1BZQVU3U2ljaUNVL05mUm5vOTJnVTlrYW5Ec2t6S2QrZmVSbDkwL25XeW41ZHI1TzM5UmhkUUNPYStZY29EVDhpSVhrRUFrR3puYWFaVlMydDkiLCJtYWMiOiIxOTU4ODNkOWI1OTNlYWUyZDUwYjFjMzMzNTBhM2ZiODRkNWI4YTViNzBkMTllODJmZjAwOWU1OGQ1MGFlYjYyIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlY0dTVvaFVFdnpNY1lCRFJvb0c2VlE9PSIsInZhbHVlIjoiQms2clU3MEVDMWt5ZzNTQ1ZzUmxCNTA1Um44YTJ3eGNiNE9EaXJTa3hFTkc1cE5VaUlpUVdPVVN0ZzR4djdDbHgrRzY3RU5reExnWWJYY1pTM0NJUHBqK0srcFNISE1JS09ramx0SEVxSzhSL0NWZmEyY1puNzQrWUxPdGVRNTgiLCJtYWMiOiJmYTRjZjQ3NDBlNWIyYmNiMTIxZTlmNWJkOWM3MzBkMTA4OTNhOTcwMmY2ODIyMDFjOWRhZGFhZmRhOTFmY2VhIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:02:15', '2025-08-07 14:02:15')
-- ","Time:":0.42} 
[2025-08-07 14:02:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":0.45} 
[2025-08-07 14:02:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.52} 
[2025-08-07 14:02:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.25} 
[2025-08-07 14:02:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7488' and `wallets`.`user_id` is not null limit 1
-- ","Time:":3.22} 
[2025-08-07 14:02:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-store' limit 1
-- ","Time:":17.2} 
[2025-08-07 14:02:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"name\":\"Bui Thanh\",\"expire_at\":\"20\\/08\\/2025\",\"vacancies\":\"11\",\"career\":\"1\",\"skill\":\"4\",\"rank\":\"2\",\"salary_currency\":\"VND\",\"salary_min\":\"1000000\",\"salary_currency_max\":\"VND\",\"salary_max\":\"2000000\",\"bonus_type\":\"onboard\",\"bonus\":\"2000000\",\"type\":\"full-time\",\"address\":[{\"area\":\"ho-chi-minh\",\"address\":\"hn\"},null],\"jd_description\":\"<p>hn<\\/p>\",\"jd_request\":\"<p>hn<\\/p>\",\"jd_welfare\":\"<p>hn<\\/p>\"}', 'http://recland.local/employer/job/store', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"2125\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"multipart\\/form-data; boundary=----WebKitFormBoundary7LRf51TmHHK1PMMB\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InA4QzgyREpHSXlFOFp5VU15NUNvYmc9PSIsInZhbHVlIjoiYkU0VlRrNkR6YXovem5Hbkk4YlFlSlhUZnUrZHY3aGVPdE9hUU1JTG81V3dnU1BZQVU3U2ljaUNVL05mUm5vOTJnVTlrYW5Ec2t6S2QrZmVSbDkwL25XeW41ZHI1TzM5UmhkUUNPYStZY29EVDhpSVhrRUFrR3puYWFaVlMydDkiLCJtYWMiOiIxOTU4ODNkOWI1OTNlYWUyZDUwYjFjMzMzNTBhM2ZiODRkNWI4YTViNzBkMTllODJmZjAwOWU1OGQ1MGFlYjYyIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlY0dTVvaFVFdnpNY1lCRFJvb0c2VlE9PSIsInZhbHVlIjoiQms2clU3MEVDMWt5ZzNTQ1ZzUmxCNTA1Um44YTJ3eGNiNE9EaXJTa3hFTkc1cE5VaUlpUVdPVVN0ZzR4djdDbHgrRzY3RU5reExnWWJYY1pTM0NJUHBqK0srcFNISE1JS09ramx0SEVxSzhSL0NWZmEyY1puNzQrWUxPdGVRNTgiLCJtYWMiOiJmYTRjZjQ3NDBlNWIyYmNiMTIxZTlmNWJkOWM3MzBkMTA4OTNhOTcwMmY2ODIyMDFjOWRhZGFhZmRhOTFmY2VhIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:02:33', '2025-08-07 14:02:33')
-- ","Time:":0.77} 
[2025-08-07 14:02:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":0.64} 
[2025-08-07 14:02:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.76} 
[2025-08-07 14:02:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.34} 
[2025-08-07 14:02:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7488' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.27} 
[2025-08-07 14:02:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '4' limit 1
-- ","Time:":0.32} 
[2025-08-07 14:02:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-store' limit 1
-- ","Time:":18.43} 
[2025-08-07 14:02:42] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"name\":\"Bui Thanh\",\"expire_at\":\"20\\/08\\/2025\",\"vacancies\":\"11\",\"career\":\"1\",\"skill\":\"4\",\"rank\":\"2\",\"salary_currency\":\"VND\",\"salary_min\":\"1000000\",\"salary_currency_max\":\"VND\",\"salary_max\":\"2000000\",\"bonus_type\":\"onboard\",\"bonus\":\"2000000\",\"type\":\"full-time\",\"address\":[{\"area\":\"ho-chi-minh\",\"address\":\"hn\"},null],\"jd_description\":\"<p>hn<\\/p>\",\"jd_request\":\"<p>hn<\\/p>\",\"jd_welfare\":\"<p>hn<\\/p>\"}', 'http://recland.local/employer/job/store', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"2125\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"multipart\\/form-data; boundary=----WebKitFormBoundary7LRf51TmHHK1PMMB\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InA4QzgyREpHSXlFOFp5VU15NUNvYmc9PSIsInZhbHVlIjoiYkU0VlRrNkR6YXovem5Hbkk4YlFlSlhUZnUrZHY3aGVPdE9hUU1JTG81V3dnU1BZQVU3U2ljaUNVL05mUm5vOTJnVTlrYW5Ec2t6S2QrZmVSbDkwL25XeW41ZHI1TzM5UmhkUUNPYStZY29EVDhpSVhrRUFrR3puYWFaVlMydDkiLCJtYWMiOiIxOTU4ODNkOWI1OTNlYWUyZDUwYjFjMzMzNTBhM2ZiODRkNWI4YTViNzBkMTllODJmZjAwOWU1OGQ1MGFlYjYyIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlY0dTVvaFVFdnpNY1lCRFJvb0c2VlE9PSIsInZhbHVlIjoiQms2clU3MEVDMWt5ZzNTQ1ZzUmxCNTA1Um44YTJ3eGNiNE9EaXJTa3hFTkc1cE5VaUlpUVdPVVN0ZzR4djdDbHgrRzY3RU5reExnWWJYY1pTM0NJUHBqK0srcFNISE1JS09ramx0SEVxSzhSL0NWZmEyY1puNzQrWUxPdGVRNTgiLCJtYWMiOiJmYTRjZjQ3NDBlNWIyYmNiMTIxZTlmNWJkOWM3MzBkMTA4OTNhOTcwMmY2ODIyMDFjOWRhZGFhZmRhOTFmY2VhIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:02:42', '2025-08-07 14:02:42')
-- ","Time:":0.63} 
[2025-08-07 14:02:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":0.64} 
[2025-08-07 14:02:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.77} 
[2025-08-07 14:02:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.34} 
[2025-08-07 14:02:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7488' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.4} 
[2025-08-07 14:02:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '4' limit 1
-- ","Time:":0.29} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":16.93} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjlFQ3RIKzFIS293TllOM0tmRE9mekE9PSIsInZhbHVlIjoiUUlxMUcvYzVxZ2hGM1dzRk00Wnh0MWR0MFhjT1ZtTm1wYmdJbEM1M0lhS1FISGpGS1JiSDZvcGRYdlZqOSthMXB6S1M1VE9qWktRRGtWU1Y3Mzd5cmRPMGZFVHloN1ZRdWQrU2VtKzBzampwbFdFemJKeko3L2RpYlJCbHo5dUoiLCJtYWMiOiJlMmFkZDRlNGNkODgzY2E2NGQ2MmZjMDNjOGFhYjgzMzY4NDJmMDc0MWRjZWY4N2U5NDNlZDVlNTZkNDkyZGQzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlhyN09ZeWRrdVppd3cvUlkxallqWHc9PSIsInZhbHVlIjoiZ3paOTI0QzBmYlRsZ2xaaXJXekFIcHZzeHpaaENSTXNYN2RQeStxZnNRbXBkOXVISXg1cWJ6clZ0c1FGdHpMR3FQZVRRdDRBMFhZV3VDTzJPSjk0S0FWb2VoQ3MzSS9URDBpaCtjZHVxRmNMNlRXN1Rya3VIcU1iTDJzTTdXdEkiLCJtYWMiOiI5YzBjNDdlODdkNjIzN2I4NDEyMDVmMGFhNTNkNDg3OWJkZmNhMTIzZjgzMDUyNDg1ZjMyZTA3OTZhNWJhNjU4IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:02:43', '2025-08-07 14:02:43')
-- ","Time:":0.35} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":0.38} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.43} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.23} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7488' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.29} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":0.3} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `employer_id` = '7488'
-- ","Time:":3.89} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '7488'
-- ","Time:":3.46} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '0' and `employer_id` = '7488'
-- ","Time:":3.32} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.79} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.72} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.73} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.74} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.71} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.7} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.7} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7488' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.71} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7488' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.59} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7488' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.53} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.75} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.71} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.7} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.69} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.73} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.7} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.7} 
[2025-08-07 14:02:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7488)
-- ","Time:":0.7} 
[2025-08-07 14:03:50] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/logout', 'http://recland.local/employer/job', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNqMkU0ekdxakJkbUNtVzdxUmZnT1E9PSIsInZhbHVlIjoidG02VEtoMzRMeXVKTE83b3gyaWJtZEdqbzB3OVF2eEg4bEdNc3FBUWk4UmxKc2FWNFB3MnVnakJCVGwxZitqZEFqZi9XZXdLQkU1TXcxU0x3WHJsa1hrRXM4ZDdRY3h4TUZvbHBMb1VlZVI3eElXSjJBbUdmWDBITFVkR29va2IiLCJtYWMiOiJhMmE5NTBjNWZkYTRlODQ4NzU1NjIwOWNjYWUzYTJhOTA0ZmU4MGY0YTM0OGUxYWY5NmE1ODdjMzdmZDMwNWEyIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IitwWnlMOFg2M0IyM0hqNHZCdVNVQXc9PSIsInZhbHVlIjoiNThGRGN0VDFxVWFoRURLRDU3NTRzMm8ySFMrOTVSb2xHTU1LL0wwZ0FFOVJ0d3NaVmRaYjdESkxDaVVHY3RYTDFCR2FCVW9Qc2pjMTJ0YXdHbCs0cGJXNWJjWGZtcnFzODFrNlJMVzZaN252c1hERzZaMWQ4RExSaWJNYXlGYlMiLCJtYWMiOiIwNWZlOTE1OGQzY2UzMDc1NzEwNzU3NDA1MDlmMjhlYzgzYjVjMWY4ODgzMDI4OWE1OTU2MTI5YzE0OGQyYTAzIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:03:50', '2025-08-07 14:03:50')
-- ","Time:":18.89} 
[2025-08-07 14:03:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7488' limit 1
-- ","Time:":1.26} 
[2025-08-07 14:03:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":21.92} 
[2025-08-07 14:03:51] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer', 'http://recland.local/employer/job', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJ2NWhiK0Vaa3o3WVdHeXFLYUdaMWc9PSIsInZhbHVlIjoic1M0R1AvVmNGSnFZQ0M1NkhwRmNkS1RPanl2NE9kcllOR2hINkE4L0s1Y2NDdGtibE1WRng2Z3RJV3ZoL25BRlo0ODhMUHFubkE5andZUUowR3ltazh5NEprb0lGZ05HZGxDNXVpbExqRWl2OXhCZGZMdzlkVXlFQVdGTjVHdjMiLCJtYWMiOiI1MWEyNGZiNzJiODg5NjdiMDM4MzA0Y2M3ZTFjNDcyYzYyNDcwOGVkNzExZjIzNWI1M2Q0OGI3NWMwN2E1NDdiIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImM1cVdFcXI1TW9yeWpRVElVVzNTL3c9PSIsInZhbHVlIjoicWJCQ1FLakppZ1RINmQyYmg0N29wU1NTa01aNEhPQXBIdU0xTVRTcFBsU3hVOFRGM3l5Z3A1S2oxVHZuS0NmWFJsd1JYZ0VDaDVSdnA3Sk90NzJ3S3ZWNEtQQWdKRnpNZmVEUGpuZmFQcHNSeU9IallIQmswR2Z3QmhPOVV0bjIiLCJtYWMiOiJkMWMzOGZkYzRhYjAwMzA0YTIyNmY5NGJmMzkyYWY3NzhiMDE2NmM2N2ZhMzg5MTUyZGVhMTk4ZDcwZjNiODMwIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:03:51', '2025-08-07 14:03:51')
-- ","Time:":0.63} 
[2025-08-07 14:03:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.58} 
[2025-08-07 14:03:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.54} 
[2025-08-07 14:03:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.66} 
[2025-08-07 14:03:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.92} 
[2025-08-07 14:03:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.64} 
[2025-08-07 14:03:55] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"action\":\"register\"}', 'http://recland.local/employer/register?action=register', 'http://recland.local/employer', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlkxQklUaTZ0MWZvejVSUUxqUXJlZmc9PSIsInZhbHVlIjoidlJ5dzl1cTIwSnN6WE13WldYMzFJWEIwWkFuekV6UzdEbFVkaHpVUjQ2L1JZNEtCYTJTK082WFhWU21helFNc2FRYy9kdWxOb1BjaUpiMTkvczdXWk0ySVdGTHZQU2taUkJwZTRpWjgvai8vUUg0RG5uWEFzSnIwZDBzYlhwQlgiLCJtYWMiOiJhNjY5OTViMGY1NWNlMWNhM2NiMzY5NzM4ODQyMDNiOTQ2OTE3MDFiMDliODBmNmRlNTY5NmJjMTljMGRmNDViIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ii9oWlVGL3VINmJBNmR3UDZpK2RLNkE9PSIsInZhbHVlIjoiOFdrZXlCd0xPV3hzMTFGQVVxcWlla1hscm0wZUhQMDVGMTlIQTR3TnA1ZmJOakc0bTBrU0NKUERkcFJnK3FWUHNPMDJLNXVrejZRZ0QydU1JWnZScklQL3BJbnovUzBvWGI0R25wMTZpQ3VQQ3lzVGxEcmN5REV4YjRXQk9paEciLCJtYWMiOiI2M2E2NTE4NTYzZmIwY2Y1NzU5ZTYyZTE4NDY5MzVlYzU5OGYyNjYwYTllZjJhZjI2NDcwMjJjYmI5YzJhMjk1IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:03:55', '2025-08-07 14:03:55')
-- ","Time:":24.03} 
[2025-08-07 14:03:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-register' limit 1
-- ","Time:":0.79} 
[2025-08-07 14:03:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.49} 
[2025-08-07 14:03:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.81} 
[2025-08-07 14:03:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.56} 
[2025-08-07 14:03:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.45} 
[2025-08-07 14:10:25] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"name\":\"Admin Hri\",\"mobile\":\"0932329007\",\"email\":\"<EMAIL>\",\"company_name\":\"HRI\",\"work_position\":\"dev\",\"work_location\":\"hn\",\"password\":\"111111\",\"confirm_password\":\"111111\",\"accept_terms\":\"on\"}', 'http://recland.local/employer/register', 'http://recland.local/employer/register?action=register', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"217\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/register?action=register\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVveGFuWHNuRHdFRlNrTEZQaTQvalE9PSIsInZhbHVlIjoiT0k2TXhVRlZWTS91NzJhZHJHQUJqUEV4REp6WUNGcDR3ZjdrczIwZFFNbllrTm1HLzBKZHc3UGtzUTNrcFhKMUNpTUQ3NHhYcXpJQ1NBSWxoU2ZzS0xYNXZ5Zms5NUp0WEtUMUhaTFdDRVZEbTd0MXZhN3UycExyNHMybnNjYk0iLCJtYWMiOiIwYzE0NDg0MjQyN2Y5OTJiYjYyOTU0YTM1Nzg1YmQyMzg2N2I1NWMwZGE4NjBmNmM3MzVkOWRjZDk2NWUyODUxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImZqWnRUd2cyUUlUa1NuV3NVTDVkeGc9PSIsInZhbHVlIjoiKzFqNzR1OXp4ZmZVYnNnbytlTVVaQXVsUFhDK2lScS9qRzlrT0pDTWdocGhFcWRHS0lzTjRGdE92VFc4QUpkSFRNTEZJRXU5enV0WVFQc2JWV003bEFEMHUyZHJPNndxcHF4TFN6NElSdXdqQ3dZS3NlTVluMjNHYmhVSy9HRVgiLCJtYWMiOiJlMDMzMGZkYzhiNWViOWQ4ZjJlYjg3YTk2ZWZkZTkzODdkY2YwY2JmOWQxOTQyZDZkODlkY2IxM2U1MTQzOTNkIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:10:25', '2025-08-07 14:10:25')
-- ","Time:":18.41} 
[2025-08-07 14:10:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1
-- ","Time:":0.94} 
[2025-08-07 14:10:26] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `users` (`name`, `email`, `mobile`, `company_name`, `mst`, `work_position`, `address`, `work_location`, `referral_code`, `password`, `type`, `token`, `last_login_at`, `updated_at`, `created_at`) values ('Admin Hri', '<EMAIL>', '0932329007', 'HRI', '', 'dev', 'hn', 'hn', '', '$2y$10$Y9UzNa8Jet33Fc55X6854.KUXcnfqMixrrplWxDYoj8WXs2JJ70s2', 'employer', 'xMPpMpyae57GoKHEwTq6JVIxssHqvd3S', '2025-08-07 14:10:26', '2025-08-07 14:10:26', '2025-08-07 14:10:26')
-- ","Time:":0.9} 
[2025-08-07 14:10:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.61} 
[2025-08-07 14:10:26] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('[]', '{\"name\":\"Admin Hri\",\"email\":\"<EMAIL>\",\"mobile\":\"0932329007\",\"company_name\":\"HRI\",\"mst\":null,\"work_position\":\"dev\",\"address\":\"hn\",\"work_location\":\"hn\",\"referral_code\":\"\",\"password\":\"$2y$10$Y9UzNa8Jet33Fc55X6854.KUXcnfqMixrrplWxDYoj8WXs2JJ70s2\",\"type\":\"employer\",\"token\":\"xMPpMpyae57GoKHEwTq6JVIxssHqvd3S\",\"last_login_at\":\"2025-08-07 14:10:26\",\"id\":7489}', 'created', '7489', 'App\\Models\\User', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/employer/register', '2025-08-07 14:10:26', '2025-08-07 14:10:26')
-- ","Time:":1.27} 
[2025-08-07 14:10:26] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `employer_types` (`user_id`, `type`, `updated_at`, `created_at`) values ('7489', 'manager', '2025-08-07 14:10:26', '2025-08-07 14:10:26')
-- ","Time:":0.62} 
[2025-08-07 14:10:26] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `wallets` (`user_id`, `type`, `updated_at`, `created_at`) values ('7489', 'employer', '2025-08-07 14:10:26', '2025-08-07 14:10:26')
-- ","Time:":0.62} 
[2025-08-07 14:10:26] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('[]', '{\"user_id\":7489,\"type\":\"employer\",\"id\":5805}', 'created', '5805', 'App\\Models\\Wallet', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/employer/register', '2025-08-07 14:10:26', '2025-08-07 14:10:26')
-- ","Time:":0.97} 
[2025-08-07 14:10:26] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[RecLand] [THÔNG BÁO NHÀ TUYỂN DỤNG MỚI]', '<!DOCTYPE html>

<html xmlns=\"http://www.w3.org/1999/xhtml\"

      class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface no-generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths js csstransforms csstransforms3d csstransitions responsejs \">

<meta charset=\"UTF-8\"/>

<head>

    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/>

    <style type=\"text/css\" media=\"only screen and (max-width: 480px)\">

        /* Mobile styles */

        @media only screen and (max-width: 480px) {

            [class=\"w320\"] {

                width: 320px !important;

            }



            [class=\"mobile-block\"] {

                width: 100% !important;

                display: block !important;

            }

        }

    </style>

</head>

<body style=\"margin: 0; cursor: auto; overflow: visible;\" class=\"ui-sortable\" data-new-gr-c-s-check-loaded=\"14.981.0\">

<div align=\"center\" style=\"background-color: #e5e5e5;\">

    <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 600px\">

        <tr>

            <td>

                <div align=\"center\">































                    <a href=\"http://recland.local\" target=\"_blank\"

                       style=\"border-color: transparent; border-width: 20px 20px 10px 20px; border-style: solid; display: inline-block;\">

                        <img width=\"140\" alt=\"\" src=\"https://i.imgur.com/9aOUeZI.png\"/>

                    </a>

                </div>

                <div style=\"background-color: #fff\">

                    <p style=\"width: 100%; background: linear-gradient(90.01deg, #1C4355 -0.66%, #EA6D56 104.24%); height: 8px\">

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; margin-top: 25px; border-width: 0 40px; border-style: solid; border-color: #fff\">

                        Name: <b>Admin Hri</b>,

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Email: <EMAIL>

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Mobile: 0932329007

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Company: HRI

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Nguồn: 

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Vị trí công tác: dev

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Khu vực: hn

                    </p>



                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Regards,</p>



                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        RECLAND</p>



                    <hr style=\"border-width: 0; border-bottom: 1px solid #EDEDED; margin: 0\"/>

                </div>

            </td>

        </tr>

    </table>

</div>

</body>

</html>



', '86b1a728a6c332099b08ee07058a9b65', '0', '2025-08-07 14:10:26', '2025-08-07 14:10:26')
-- ","Time:":1.0} 
[2025-08-07 14:10:27] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\"uuid\":\"d2355777-ff53-400e-bdf8-63da60cf63ac\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:4525:\\\"<!DOCTYPE html>\\r\\n<html xmlns=\\\"http:\\/\\/www.w3.org\\/1999\\/xhtml\\\"\\r\\n      class=\\\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface no-generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths js csstransforms csstransforms3d csstransitions responsejs \\\">\\r\\n<meta charset=\\\"UTF-8\\\"\\/>\\r\\n<head>\\r\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\"\\/>\\r\\n    <style type=\\\"text\\/css\\\" media=\\\"only screen and (max-width: 480px)\\\">\\r\\n        \\/* Mobile styles *\\/\\r\\n        @media only screen and (max-width: 480px) {\\r\\n            [class=\\\"w320\\\"] {\\r\\n                width: 320px !important;\\r\\n            }\\r\\n\\r\\n            [class=\\\"mobile-block\\\"] {\\r\\n                width: 100% !important;\\r\\n                display: block !important;\\r\\n            }\\r\\n        }\\r\\n    <\\/style>\\r\\n<\\/head>\\r\\n<body style=\\\"margin: 0; cursor: auto; overflow: visible;\\\" class=\\\"ui-sortable\\\" data-new-gr-c-s-check-loaded=\\\"14.981.0\\\">\\r\\n<div align=\\\"center\\\" style=\\\"background-color: #e5e5e5;\\\">\\r\\n    <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"min-width: 600px\\\">\\r\\n        <tr>\\r\\n            <td>\\r\\n                <div align=\\\"center\\\">\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n                    <a href=\\\"http:\\/\\/recland.local\\\" target=\\\"_blank\\\"\\r\\n                       style=\\\"border-color: transparent; border-width: 20px 20px 10px 20px; border-style: solid; display: inline-block;\\\">\\r\\n                        <img width=\\\"140\\\" alt=\\\"\\\" src=\\\"https:\\/\\/i.imgur.com\\/9aOUeZI.png\\\"\\/>\\r\\n                    <\\/a>\\r\\n                <\\/div>\\r\\n                <div style=\\\"background-color: #fff\\\">\\r\\n                    <p style=\\\"width: 100%; background: linear-gradient(90.01deg, #1C4355 -0.66%, #EA6D56 104.24%); height: 8px\\\">\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; margin-top: 25px; border-width: 0 40px; border-style: solid; border-color: #fff\\\">\\r\\n                        Name: <b>Admin Hri<\\/b>,\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Email: <EMAIL>\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Mobile: 0932329007\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Company: HRI\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Ngu\\u1ed3n: \\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        V\\u1ecb tr\\u00ed c\\u00f4ng t\\u00e1c: dev\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Khu v\\u1ef1c: hn\\r\\n                    <\\/p>\\r\\n\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Regards,<\\/p>\\r\\n\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        RECLAND<\\/p>\\r\\n\\r\\n                    <hr style=\\\"border-width: 0; border-bottom: 1px solid #EDEDED; margin: 0\\\"\\/>\\r\\n                <\\/div>\\r\\n            <\\/td>\\r\\n        <\\/tr>\\r\\n    <\\/table>\\r\\n<\\/div>\\r\\n<\\/body>\\r\\n<\\/html>\\r\\n\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:5:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:2:\\\"cc\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"Cc\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:49:\\\"[RecLand] [TH\\u00d4NG B\\u00c1O NH\\u00c0 TUY\\u1ec2N D\\u1ee4NG M\\u1edaI]\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:11:\\\"{\\\"Cc\\\":[{}]}\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}', '2025-08-07 14:10:27', '2025-08-07 14:10:27')
-- ","Time:":1.85} 
[2025-08-07 14:10:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` where `hash` = 'bc1df78e55cbcd85d3218d86aa7d619e' and `created_at` >= '2025-08-07 14:05:27'
-- ","Time:":211.4} 
[2025-08-07 14:10:27] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[]', '[Recland] XÁC THỰC TÀI KHOẢN NHÀ TUYỂN DỤNG', '<!DOCTYPE html>

<html>



<body width=\"100%\"

    style=\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\">

    <center style=\"width: 100%; background-color: #f1f1f1;\">

        <div style=\"max-width: 600px; margin: 0 auto;\">

            <table align=\"center\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\"

                style=\"margin: auto;\">

                <tr>

                    <td>

                        <div style=\"text-align: center;padding: 25px 0;background: #FCFCFE;\">

                            <img style=\"max-width: 100%\"

                                src=\"http://recland.local/frontend/assets_v2/images/graphics/logo-250.png?v=68945163ae7eb\">

                        </div>

                    </td>

                </tr>

            </table>

            <div style=\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\">

                <div

                    style=\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;

                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=68945163ae813);

                background-repeat: no-repeat;background-size: 100%;\">

                    <table>

                        <tr>

                            <td>

                                <div

                                    style=\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\">

                                    <div style=\"margin-bottom: 14px\">

                                                                            </div>

                                    <div>

                                                                            </div>



                                </div>

                            </td>

                        </tr>

                    </table>

                    <table style=\"width: 100%\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"

                        width=\"100%\">

                        <tr>

                            <td>

                                    <div

        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">

        <p>Xin chào, Admin Hri</p>

        <p>Xin chúc mừng bạn đã tạo tài khoản Nhà Tuyển Dụng Recland thành công.</p>

        <p>Chào mừng bạn đến với Cộng đồng Recland - Nền tảng tuyển dụng trực tuyến hàng đầu, kết nối Nhà tuyển dụng với

            đội ngũ HR Freelancer trên toàn quốc.</p>

                    <p style=\"margin-bottom: 24px\">Hãy xác nhận rằng bạn đang sử dụng email này cho tài khoản Recland của mình để

                làm tài khoản đăng nhập. Chúng tôi sẽ gửi những thông báo quan trọng đến bạn.</p>

            <p style=\"margin-bottom: 24px\">Xác nhận địa chỉ email để nhận ngay <strong>200.000đ</strong></p>

            <p style=\"text-align: center;margin-bottom: 0\">

                <a style=\"

            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);

            font-weight: 400;

            font-size: 16px;

            margin: 0 auto;

            color: #ffffff;

            display: block;

            width: 300px;

            height: 46px;

            line-height: 46px;

            border-radius: 8px;

            margin-bottom: 16px;

            text-decoration: none;

                \"

                    href=\"http://recland.local/verify-employer?token=xMPpMpyae57GoKHEwTq6JVIxssHqvd3S\">

                    Xác minh Email của Bạn

                </a>

            </p>

                

        <p><b>Trân trọng,</b></p>

        <p><i>Đội ngũ Recland.</i></p>

    </div>

    <div style=\"border: 5px solid #F7F7F7;\"></div>

    <div

        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">

        <p>Hello, Admin Hri</p>

        <p>Congratulations on successfully creating your employer account on Recland.</p>

        <p>Welcome to the Recland Community - The leading online Recruitment Platform that connects Employers with a

            nationwide network of freelance headhunters.</p>

                    <p style=\"margin-bottom: 24px\">Please confirm that you are using this email for your Recland account to

                make your login account. We will send important notifications to you.</p>

            <p style=\"margin-bottom: 24px\">Confirm your email address to receive <strong>200.000đ</strong></p>

            <p style=\"text-align: center;margin-bottom: 0\">

                <a style=\"

            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);

            font-weight: 400;

            font-size: 16px;

            margin: 0 auto;

            color: #ffffff;

            display: block;

            width: 300px;

            height: 46px;

            line-height: 46px;

            border-radius: 8px;

            margin-bottom: 16px;

            text-decoration: none;

                \"

                    href=\"http://recland.local/verify-employer?token=xMPpMpyae57GoKHEwTq6JVIxssHqvd3S\">

                    Verify Your Email

                </a>

            </p>

                

        <p><b>Thanks and best regards,</b></p>

        <p><i>Recland Team,</i></p>

    </div>

                            </td>

                        </tr>

                        <tr>

                            <td>

                                <div style=\"padding:12px 0\">

                                    <div

                                        style=\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=68945163ae85d);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\">

                                        <div style=\"margin-bottom: 12px;text-align: center\">

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=68945163ae87a\"></a>

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=68945163ae896\"></a>

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=68945163ae8b1\"></a>

                                        </div>

                                        <p

                                            style=\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\">

                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>

                                        <p

                                            style=\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\">

                                            © 2022 Recland.co</p>

                                    </div>

                                </div>



                            </td>

                        </tr>

                    </table>

                </div>

            </div>

        </div>

    </center>

</body>



</html>

', '085eb6dc2b9f6d80a5375d44b9a504ef', '0', '2025-08-07 14:10:27', '2025-08-07 14:10:27')
-- ","Time:":1.08} 
[2025-08-07 14:10:28] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\"uuid\":\"1f3ab239-974e-465e-bc2f-1b14b8795a55\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:8261:\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/assets_v2\\/images\\/graphics\\/logo-250.png?v=68945163ae7eb\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=68945163ae813);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Xin ch\\u00e0o, Admin Hri<\\/p>\\r\\n        <p>Xin ch\\u00fac m\\u1eebng b\\u1ea1n \\u0111\\u00e3 t\\u1ea1o t\\u00e0i kho\\u1ea3n Nh\\u00e0 Tuy\\u1ec3n D\\u1ee5ng Recland th\\u00e0nh c\\u00f4ng.<\\/p>\\r\\n        <p>Ch\\u00e0o m\\u1eebng b\\u1ea1n \\u0111\\u1ebfn v\\u1edbi C\\u1ed9ng \\u0111\\u1ed3ng Recland - N\\u1ec1n t\\u1ea3ng tuy\\u1ec3n d\\u1ee5ng tr\\u1ef1c tuy\\u1ebfn h\\u00e0ng \\u0111\\u1ea7u, k\\u1ebft n\\u1ed1i Nh\\u00e0 tuy\\u1ec3n d\\u1ee5ng v\\u1edbi\\r\\n            \\u0111\\u1ed9i ng\\u0169 HR Freelancer tr\\u00ean to\\u00e0n qu\\u1ed1c.<\\/p>\\r\\n                    <p style=\\\"margin-bottom: 24px\\\">H\\u00e3y x\\u00e1c nh\\u1eadn r\\u1eb1ng b\\u1ea1n \\u0111ang s\\u1eed d\\u1ee5ng email n\\u00e0y cho t\\u00e0i kho\\u1ea3n Recland c\\u1ee7a m\\u00ecnh \\u0111\\u1ec3\\r\\n                l\\u00e0m t\\u00e0i kho\\u1ea3n \\u0111\\u0103ng nh\\u1eadp. Ch\\u00fang t\\u00f4i s\\u1ebd g\\u1eedi nh\\u1eefng th\\u00f4ng b\\u00e1o quan tr\\u1ecdng \\u0111\\u1ebfn b\\u1ea1n.<\\/p>\\r\\n            <p style=\\\"margin-bottom: 24px\\\">X\\u00e1c nh\\u1eadn \\u0111\\u1ecba ch\\u1ec9 email \\u0111\\u1ec3 nh\\u1eadn ngay <strong>200.000\\u0111<\\/strong><\\/p>\\r\\n            <p style=\\\"text-align: center;margin-bottom: 0\\\">\\r\\n                <a style=\\\"\\r\\n            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);\\r\\n            font-weight: 400;\\r\\n            font-size: 16px;\\r\\n            margin: 0 auto;\\r\\n            color: #ffffff;\\r\\n            display: block;\\r\\n            width: 300px;\\r\\n            height: 46px;\\r\\n            line-height: 46px;\\r\\n            border-radius: 8px;\\r\\n            margin-bottom: 16px;\\r\\n            text-decoration: none;\\r\\n                \\\"\\r\\n                    href=\\\"http:\\/\\/recland.local\\/verify-employer?token=xMPpMpyae57GoKHEwTq6JVIxssHqvd3S\\\">\\r\\n                    X\\u00e1c minh Email c\\u1ee7a B\\u1ea1n\\r\\n                <\\/a>\\r\\n            <\\/p>\\r\\n                \\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Hello, Admin Hri<\\/p>\\r\\n        <p>Congratulations on successfully creating your employer account on Recland.<\\/p>\\r\\n        <p>Welcome to the Recland Community - The leading online Recruitment Platform that connects Employers with a\\r\\n            nationwide network of freelance headhunters.<\\/p>\\r\\n                    <p style=\\\"margin-bottom: 24px\\\">Please confirm that you are using this email for your Recland account to\\r\\n                make your login account. We will send important notifications to you.<\\/p>\\r\\n            <p style=\\\"margin-bottom: 24px\\\">Confirm your email address to receive <strong>200.000\\u0111<\\/strong><\\/p>\\r\\n            <p style=\\\"text-align: center;margin-bottom: 0\\\">\\r\\n                <a style=\\\"\\r\\n            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);\\r\\n            font-weight: 400;\\r\\n            font-size: 16px;\\r\\n            margin: 0 auto;\\r\\n            color: #ffffff;\\r\\n            display: block;\\r\\n            width: 300px;\\r\\n            height: 46px;\\r\\n            line-height: 46px;\\r\\n            border-radius: 8px;\\r\\n            margin-bottom: 16px;\\r\\n            text-decoration: none;\\r\\n                \\\"\\r\\n                    href=\\\"http:\\/\\/recland.local\\/verify-employer?token=xMPpMpyae57GoKHEwTq6JVIxssHqvd3S\\\">\\r\\n                    Verify Your Email\\r\\n                <\\/a>\\r\\n            <\\/p>\\r\\n                \\r\\n        <p><b>Thanks and best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland Team,<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=68945163ae85d);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=68945163ae87a\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=68945163ae896\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=68945163ae8b1\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:4:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:54:\\\"[Recland] X\\u00c1C TH\\u1ef0C T\\u00c0I KHO\\u1ea2N NH\\u00c0 TUY\\u1ec2N D\\u1ee4NG\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:2:\\\"[]\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}', '2025-08-07 14:10:28', '2025-08-07 14:10:28')
-- ","Time:":10.77} 
[2025-08-07 14:10:28] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` where `hash` = '3431299d762c9456a4fc5d5138220293' and `created_at` >= '2025-08-07 14:05:28'
-- ","Time:":96.69} 
[2025-08-07 14:10:28] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":22.9} 
[2025-08-07 14:10:28] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"from\":\"registed-success\"}', 'http://recland.local/employer?from=registed-success', 'http://recland.local/employer/register?action=register', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/register?action=register\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkJTdkhCZUF4Sy9GY2Vqa2l0MXpoQ1E9PSIsInZhbHVlIjoicEJnTmI1TGQ4d29ZeHRKQ3FUQ3JDNDQva1FINWdiOFRqRld5MEpsamVVZUdaWWJrbkdqaHJPeUMybWs2RDBHdEt3K3RUN3BDK3cyMHMvWXI1TTgyQ01XNCtJWGlUK0NhdjFTT0s0OVZsVU1ULzZtZHl0UlJkUkhiYzhrUllId3ciLCJtYWMiOiJhMmMzZDE4NDNiZmJiMjE3ZGE1NjUzZjZjN2JkYTM4MDcwOGUyZjYwNTQ1YTEzOTQxZDMzYjQ3NmY1MTc1OGE0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkNqUWM1NFd1TGFKaHMyY1JERVd1cEE9PSIsInZhbHVlIjoiY28vM3R1VWsvSHR6dTRiT1RzUUgvSjBOZHFSaVl0Q2xtNmc5U0tkMFVlZjBCSkh3Q3lQVDAzbWt2QVRFMzM1cGV2dkV4ZnlmMDBtODN6em04Mi9EVGtjYTlnUmhhR0dFeGZ5eGZ5NGtOQzFvMWlmKys0TmlEd3MzTnptQ2pZM3QiLCJtYWMiOiI0YzE1NDhkYWRiOWJlZWYxYTJiNGVhYTQ0ZmJhMmU2MDYyZDg1MzExNGJhM2VhNjFlNGZkZDRlNWQ0MmJhMjljIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:10:28', '2025-08-07 14:10:28')
-- ","Time:":0.66} 
[2025-08-07 14:10:28] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.58} 
[2025-08-07 14:10:28] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.49} 
[2025-08-07 14:10:28] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.5} 
[2025-08-07 14:10:28] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.52} 
[2025-08-07 14:10:28] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.46} 
[2025-08-07 14:11:15] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"action\":\"register\"}', 'http://recland.local/employer/register?action=register', '', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjlLWHkySVArMnZZTGVxMnoyWncwVmc9PSIsInZhbHVlIjoiN3IwYWd5TFVuTUNacXVoUld0dXF1V0VEWUJGck5pRzZnbFNqT29tQmxYQUlCWU9jNlVKVFo0VU0zaTBKTkRjcG1vZGh3NERCM2dqejBVazV4MFR5dExNTUY1dkp0SHd2WFQ5NG1PdldLTDUzbXZ4WW5hZFcrV2RzcjdTOHkwNXAiLCJtYWMiOiJhOTRmZWEzOWQ4MmEyY2IwNmY3MWU2ZDlhMjk1YjE4MGRhMTliNTc1MjkyMTEwNzY3MWYyNGYzOTZlNTRiYjIxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ikk4VW9oM2NLWitSZzJDeHI1V0RMOHc9PSIsInZhbHVlIjoiY1BZb2U0Snd5ZnExODZNaFBTTzJXVW8wOGNCMCtuSmJlRktzSzh5TDFwYm4wcDdMK1VjMzkySzhEUzBESXRLKzkrSW5Qb0NtOHNpYlpMNFZHOXFNWlliUFVkSzhxa01Ub1ZqUXlucFR4ZFZnMUtzUUFNd0V4MWVUSkMyNUtSVmMiLCJtYWMiOiI0ZGUwZGVmMGQwNDk2ZWMxZTgxMjAxNjkzNWU2MDU1ZmY3NzMzZGYxMTgyNWY3Yjk5MTI4OGIwNjVlOGE1MzRkIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:11:15', '2025-08-07 14:11:15')
-- ","Time:":19.87} 
[2025-08-07 14:11:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-register' limit 1
-- ","Time:":0.8} 
[2025-08-07 14:11:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.51} 
[2025-08-07 14:11:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.59} 
[2025-08-07 14:11:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.52} 
[2025-08-07 14:11:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.44} 
[2025-08-07 14:11:22] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"name\":\"Admin Hri\",\"mobile\":\"0932329007\",\"email\":\"<EMAIL>\",\"company_name\":\"HRI\",\"work_position\":\"dev\",\"work_location\":\"hn\",\"password\":\"111111\",\"confirm_password\":\"111111\",\"accept_terms\":\"on\"}', 'http://recland.local/employer/register', 'http://recland.local/employer/register?action=register', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"217\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/register?action=register\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkJxTWpweUg3WXNzWU0wZzMwOW9Bb0E9PSIsInZhbHVlIjoiZkJHLzZFRThKVGVKT2RzZVNjS29HampscXZwSnJ4NnlQakthK295R21pMXpTUTgxeUhzWnJJZWVqSjNncDFOWXBRTGR5Y2QzRkw5OFVZNmEyUEN0UVR4SlBBVVZOdDQvVUk5dW8vYkZIQkhRQ3Exd3lKOU42M1ZFaWwrbkI4Q3YiLCJtYWMiOiIxMmQxMjM5MGU2ZDlkNmU3Nzg2MWRkMGJkNGE3Y2VkMDA3MTUwODViNDBhN2E4N2M2Mjc4YzQ2OWJiZGU0NjVhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkIzeHJQOUNFR29NazVmeHhSckRCUGc9PSIsInZhbHVlIjoiYzdPNFpqS0hTZURZSFJZc1dPMWhZUitpcTFYRlZMaGpKMmdCbUloekJ6a2o4b1E4c0IvOXB1L3pIOHZhcEFjeEV3OCtLUkYyK1hUN0Zoc1J0azZSdDIwc3hTUGtvTDZJOVFTVmVBWUF5M21tcUZEQUoxaUNKODRjbndMdTM5Y2kiLCJtYWMiOiJmOWMwZjIzNGQ3NTk2MTMxM2M5MjczYWU0OTc1Zjk0ZmJjY2JmOTFiYjY0NzAyYWJkNjBhMWQyZTcxOWE4MTIyIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:11:22', '2025-08-07 14:11:22')
-- ","Time:":22.78} 
[2025-08-07 14:11:23] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1
-- ","Time:":0.85} 
[2025-08-07 14:11:23] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `companies` (`name`, `slug`, `updated_at`, `created_at`) values ('HRI', 'hri-wnYFgeVv', '2025-08-07 14:11:23', '2025-08-07 14:11:23')
-- ","Time:":0.67} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"name\":\"Admin Hri\",\"mobile\":\"0932329007\",\"email\":\"<EMAIL>\",\"company_name\":\"HRI\",\"work_position\":\"dev\",\"work_location\":\"hn\",\"password\":\"111111\",\"confirm_password\":\"111111\",\"accept_terms\":\"on\"}', 'http://recland.local/employer/register', 'http://recland.local/employer/register?action=register', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"217\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/register?action=register\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkJxTWpweUg3WXNzWU0wZzMwOW9Bb0E9PSIsInZhbHVlIjoiZkJHLzZFRThKVGVKT2RzZVNjS29HampscXZwSnJ4NnlQakthK295R21pMXpTUTgxeUhzWnJJZWVqSjNncDFOWXBRTGR5Y2QzRkw5OFVZNmEyUEN0UVR4SlBBVVZOdDQvVUk5dW8vYkZIQkhRQ3Exd3lKOU42M1ZFaWwrbkI4Q3YiLCJtYWMiOiIxMmQxMjM5MGU2ZDlkNmU3Nzg2MWRkMGJkNGE3Y2VkMDA3MTUwODViNDBhN2E4N2M2Mjc4YzQ2OWJiZGU0NjVhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkIzeHJQOUNFR29NazVmeHhSckRCUGc9PSIsInZhbHVlIjoiYzdPNFpqS0hTZURZSFJZc1dPMWhZUitpcTFYRlZMaGpKMmdCbUloekJ6a2o4b1E4c0IvOXB1L3pIOHZhcEFjeEV3OCtLUkYyK1hUN0Zoc1J0azZSdDIwc3hTUGtvTDZJOVFTVmVBWUF5M21tcUZEQUoxaUNKODRjbndMdTM5Y2kiLCJtYWMiOiJmOWMwZjIzNGQ3NTk2MTMxM2M5MjczYWU0OTc1Zjk0ZmJjY2JmOTFiYjY0NzAyYWJkNjBhMWQyZTcxOWE4MTIyIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:11:33', '2025-08-07 14:11:33')
-- ","Time:":20.19} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1
-- ","Time:":0.59} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `companies` (`name`, `slug`, `updated_at`, `created_at`) values ('HRI', 'hri-mpeOe3c5', '2025-08-07 14:11:33', '2025-08-07 14:11:33')
-- ","Time:":0.47} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `users` (`name`, `email`, `mobile`, `company_name`, `mst`, `work_position`, `address`, `work_location`, `referral_code`, `password`, `type`, `token`, `last_login_at`, `company_id`, `updated_at`, `created_at`) values ('Admin Hri', '<EMAIL>', '0932329007', 'HRI', '', 'dev', 'hn', 'hn', '', '$2y$10$I.05EcCX.5UOsShfUECqaO5A5vhftKS/rYjjtNnJN23BtHDOuqYlS', 'employer', '4fVhqi80gPNSMJY5ucJvmjqKIxCK7ySh', '2025-08-07 14:11:33', '698', '2025-08-07 14:11:33', '2025-08-07 14:11:33')
-- ","Time:":1.27} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.63} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('[]', '{\"name\":\"Admin Hri\",\"email\":\"<EMAIL>\",\"mobile\":\"0932329007\",\"company_name\":\"HRI\",\"mst\":null,\"work_position\":\"dev\",\"address\":\"hn\",\"work_location\":\"hn\",\"referral_code\":\"\",\"password\":\"$2y$10$I.05EcCX.5UOsShfUECqaO5A5vhftKS\\/rYjjtNnJN23BtHDOuqYlS\",\"type\":\"employer\",\"token\":\"4fVhqi80gPNSMJY5ucJvmjqKIxCK7ySh\",\"last_login_at\":\"2025-08-07 14:11:33\",\"company_id\":698,\"id\":7490}', 'created', '7490', 'App\\Models\\User', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/employer/register', '2025-08-07 14:11:33', '2025-08-07 14:11:33')
-- ","Time:":1.43} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `employer_types` (`user_id`, `type`, `updated_at`, `created_at`) values ('7490', 'manager', '2025-08-07 14:11:33', '2025-08-07 14:11:33')
-- ","Time:":0.87} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `wallets` (`user_id`, `type`, `updated_at`, `created_at`) values ('7490', 'employer', '2025-08-07 14:11:33', '2025-08-07 14:11:33')
-- ","Time:":0.83} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('[]', '{\"user_id\":7490,\"type\":\"employer\",\"id\":5806}', 'created', '5806', 'App\\Models\\Wallet', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/employer/register', '2025-08-07 14:11:33', '2025-08-07 14:11:33')
-- ","Time:":0.91} 
[2025-08-07 14:11:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[RecLand] [THÔNG BÁO NHÀ TUYỂN DỤNG MỚI]', '<!DOCTYPE html>

<html xmlns=\"http://www.w3.org/1999/xhtml\"

      class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface no-generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths js csstransforms csstransforms3d csstransitions responsejs \">

<meta charset=\"UTF-8\"/>

<head>

    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/>

    <style type=\"text/css\" media=\"only screen and (max-width: 480px)\">

        /* Mobile styles */

        @media only screen and (max-width: 480px) {

            [class=\"w320\"] {

                width: 320px !important;

            }



            [class=\"mobile-block\"] {

                width: 100% !important;

                display: block !important;

            }

        }

    </style>

</head>

<body style=\"margin: 0; cursor: auto; overflow: visible;\" class=\"ui-sortable\" data-new-gr-c-s-check-loaded=\"14.981.0\">

<div align=\"center\" style=\"background-color: #e5e5e5;\">

    <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 600px\">

        <tr>

            <td>

                <div align=\"center\">































                    <a href=\"http://recland.local\" target=\"_blank\"

                       style=\"border-color: transparent; border-width: 20px 20px 10px 20px; border-style: solid; display: inline-block;\">

                        <img width=\"140\" alt=\"\" src=\"https://i.imgur.com/9aOUeZI.png\"/>

                    </a>

                </div>

                <div style=\"background-color: #fff\">

                    <p style=\"width: 100%; background: linear-gradient(90.01deg, #1C4355 -0.66%, #EA6D56 104.24%); height: 8px\">

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; margin-top: 25px; border-width: 0 40px; border-style: solid; border-color: #fff\">

                        Name: <b>Admin Hri</b>,

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Email: <EMAIL>

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Mobile: 0932329007

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Company: HRI

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Nguồn: 

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Vị trí công tác: dev

                    </p>

                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Khu vực: hn

                    </p>



                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        Regards,</p>



                    <p style=\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\">

                        RECLAND</p>



                    <hr style=\"border-width: 0; border-bottom: 1px solid #EDEDED; margin: 0\"/>

                </div>

            </td>

        </tr>

    </table>

</div>

</body>

</html>



', '86b1a728a6c332099b08ee07058a9b65', '0', '2025-08-07 14:11:33', '2025-08-07 14:11:33')
-- ","Time:":0.88} 
[2025-08-07 14:11:34] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\"uuid\":\"9e466a01-2b61-4a4d-b34f-530b21ea2ea4\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:4525:\\\"<!DOCTYPE html>\\r\\n<html xmlns=\\\"http:\\/\\/www.w3.org\\/1999\\/xhtml\\\"\\r\\n      class=\\\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface no-generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths js csstransforms csstransforms3d csstransitions responsejs \\\">\\r\\n<meta charset=\\\"UTF-8\\\"\\/>\\r\\n<head>\\r\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\"\\/>\\r\\n    <style type=\\\"text\\/css\\\" media=\\\"only screen and (max-width: 480px)\\\">\\r\\n        \\/* Mobile styles *\\/\\r\\n        @media only screen and (max-width: 480px) {\\r\\n            [class=\\\"w320\\\"] {\\r\\n                width: 320px !important;\\r\\n            }\\r\\n\\r\\n            [class=\\\"mobile-block\\\"] {\\r\\n                width: 100% !important;\\r\\n                display: block !important;\\r\\n            }\\r\\n        }\\r\\n    <\\/style>\\r\\n<\\/head>\\r\\n<body style=\\\"margin: 0; cursor: auto; overflow: visible;\\\" class=\\\"ui-sortable\\\" data-new-gr-c-s-check-loaded=\\\"14.981.0\\\">\\r\\n<div align=\\\"center\\\" style=\\\"background-color: #e5e5e5;\\\">\\r\\n    <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"min-width: 600px\\\">\\r\\n        <tr>\\r\\n            <td>\\r\\n                <div align=\\\"center\\\">\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n                    <a href=\\\"http:\\/\\/recland.local\\\" target=\\\"_blank\\\"\\r\\n                       style=\\\"border-color: transparent; border-width: 20px 20px 10px 20px; border-style: solid; display: inline-block;\\\">\\r\\n                        <img width=\\\"140\\\" alt=\\\"\\\" src=\\\"https:\\/\\/i.imgur.com\\/9aOUeZI.png\\\"\\/>\\r\\n                    <\\/a>\\r\\n                <\\/div>\\r\\n                <div style=\\\"background-color: #fff\\\">\\r\\n                    <p style=\\\"width: 100%; background: linear-gradient(90.01deg, #1C4355 -0.66%, #EA6D56 104.24%); height: 8px\\\">\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; margin-top: 25px; border-width: 0 40px; border-style: solid; border-color: #fff\\\">\\r\\n                        Name: <b>Admin Hri<\\/b>,\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Email: <EMAIL>\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Mobile: 0932329007\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Company: HRI\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Ngu\\u1ed3n: \\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        V\\u1ecb tr\\u00ed c\\u00f4ng t\\u00e1c: dev\\r\\n                    <\\/p>\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Khu v\\u1ef1c: hn\\r\\n                    <\\/p>\\r\\n\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        Regards,<\\/p>\\r\\n\\r\\n                    <p style=\\\"font-family: Arial,Helvetica,sans-serif; font-size: 14px; text-align: justify; color: #000; border-width: 0 40px ; border-style: solid; border-color: #fff\\\">\\r\\n                        RECLAND<\\/p>\\r\\n\\r\\n                    <hr style=\\\"border-width: 0; border-bottom: 1px solid #EDEDED; margin: 0\\\"\\/>\\r\\n                <\\/div>\\r\\n            <\\/td>\\r\\n        <\\/tr>\\r\\n    <\\/table>\\r\\n<\\/div>\\r\\n<\\/body>\\r\\n<\\/html>\\r\\n\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:5:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:2:\\\"cc\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"Cc\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:49:\\\"[RecLand] [TH\\u00d4NG B\\u00c1O NH\\u00c0 TUY\\u1ec2N D\\u1ee4NG M\\u1edaI]\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:11:\\\"{\\\"Cc\\\":[{}]}\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}', '2025-08-07 14:11:34', '2025-08-07 14:11:34')
-- ","Time:":11.04} 
[2025-08-07 14:11:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` where `hash` = 'bc1df78e55cbcd85d3218d86aa7d619e' and `created_at` >= '2025-08-07 14:06:34'
-- ","Time:":161.1} 
[2025-08-07 14:11:35] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[]', '[Recland] XÁC THỰC TÀI KHOẢN NHÀ TUYỂN DỤNG', '<!DOCTYPE html>

<html>



<body width=\"100%\"

    style=\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\">

    <center style=\"width: 100%; background-color: #f1f1f1;\">

        <div style=\"max-width: 600px; margin: 0 auto;\">

            <table align=\"center\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\"

                style=\"margin: auto;\">

                <tr>

                    <td>

                        <div style=\"text-align: center;padding: 25px 0;background: #FCFCFE;\">

                            <img style=\"max-width: 100%\"

                                src=\"http://recland.local/frontend/assets_v2/images/graphics/logo-250.png?v=689451a729d72\">

                        </div>

                    </td>

                </tr>

            </table>

            <div style=\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\">

                <div

                    style=\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;

                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=689451a729d9a);

                background-repeat: no-repeat;background-size: 100%;\">

                    <table>

                        <tr>

                            <td>

                                <div

                                    style=\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\">

                                    <div style=\"margin-bottom: 14px\">

                                                                            </div>

                                    <div>

                                                                            </div>



                                </div>

                            </td>

                        </tr>

                    </table>

                    <table style=\"width: 100%\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"

                        width=\"100%\">

                        <tr>

                            <td>

                                    <div

        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">

        <p>Xin chào, Admin Hri</p>

        <p>Xin chúc mừng bạn đã tạo tài khoản Nhà Tuyển Dụng Recland thành công.</p>

        <p>Chào mừng bạn đến với Cộng đồng Recland - Nền tảng tuyển dụng trực tuyến hàng đầu, kết nối Nhà tuyển dụng với

            đội ngũ HR Freelancer trên toàn quốc.</p>

                    <p style=\"margin-bottom: 24px\">Hãy xác nhận rằng bạn đang sử dụng email này cho tài khoản Recland của mình để

                làm tài khoản đăng nhập. Chúng tôi sẽ gửi những thông báo quan trọng đến bạn.</p>

            <p style=\"margin-bottom: 24px\">Xác nhận địa chỉ email để nhận ngay <strong>200.000đ</strong></p>

            <p style=\"text-align: center;margin-bottom: 0\">

                <a style=\"

            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);

            font-weight: 400;

            font-size: 16px;

            margin: 0 auto;

            color: #ffffff;

            display: block;

            width: 300px;

            height: 46px;

            line-height: 46px;

            border-radius: 8px;

            margin-bottom: 16px;

            text-decoration: none;

                \"

                    href=\"http://recland.local/verify-employer?token=4fVhqi80gPNSMJY5ucJvmjqKIxCK7ySh\">

                    Xác minh Email của Bạn

                </a>

            </p>

                

        <p><b>Trân trọng,</b></p>

        <p><i>Đội ngũ Recland.</i></p>

    </div>

    <div style=\"border: 5px solid #F7F7F7;\"></div>

    <div

        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">

        <p>Hello, Admin Hri</p>

        <p>Congratulations on successfully creating your employer account on Recland.</p>

        <p>Welcome to the Recland Community - The leading online Recruitment Platform that connects Employers with a

            nationwide network of freelance headhunters.</p>

                    <p style=\"margin-bottom: 24px\">Please confirm that you are using this email for your Recland account to

                make your login account. We will send important notifications to you.</p>

            <p style=\"margin-bottom: 24px\">Confirm your email address to receive <strong>200.000đ</strong></p>

            <p style=\"text-align: center;margin-bottom: 0\">

                <a style=\"

            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);

            font-weight: 400;

            font-size: 16px;

            margin: 0 auto;

            color: #ffffff;

            display: block;

            width: 300px;

            height: 46px;

            line-height: 46px;

            border-radius: 8px;

            margin-bottom: 16px;

            text-decoration: none;

                \"

                    href=\"http://recland.local/verify-employer?token=4fVhqi80gPNSMJY5ucJvmjqKIxCK7ySh\">

                    Verify Your Email

                </a>

            </p>

                

        <p><b>Thanks and best regards,</b></p>

        <p><i>Recland Team,</i></p>

    </div>

                            </td>

                        </tr>

                        <tr>

                            <td>

                                <div style=\"padding:12px 0\">

                                    <div

                                        style=\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=689451a729de9);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\">

                                        <div style=\"margin-bottom: 12px;text-align: center\">

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=689451a729e06\"></a>

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=689451a729e21\"></a>

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=689451a729e3c\"></a>

                                        </div>

                                        <p

                                            style=\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\">

                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>

                                        <p

                                            style=\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\">

                                            © 2022 Recland.co</p>

                                    </div>

                                </div>



                            </td>

                        </tr>

                    </table>

                </div>

            </div>

        </div>

    </center>

</body>



</html>

', 'fe34eafbf6ef2fe8cb43d1fece6d08c3', '0', '2025-08-07 14:11:35', '2025-08-07 14:11:35')
-- ","Time:":1.08} 
[2025-08-07 14:11:35] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\"uuid\":\"80540ad3-c318-4862-b5ee-bc797fd8c195\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:8261:\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/assets_v2\\/images\\/graphics\\/logo-250.png?v=689451a729d72\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=689451a729d9a);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Xin ch\\u00e0o, Admin Hri<\\/p>\\r\\n        <p>Xin ch\\u00fac m\\u1eebng b\\u1ea1n \\u0111\\u00e3 t\\u1ea1o t\\u00e0i kho\\u1ea3n Nh\\u00e0 Tuy\\u1ec3n D\\u1ee5ng Recland th\\u00e0nh c\\u00f4ng.<\\/p>\\r\\n        <p>Ch\\u00e0o m\\u1eebng b\\u1ea1n \\u0111\\u1ebfn v\\u1edbi C\\u1ed9ng \\u0111\\u1ed3ng Recland - N\\u1ec1n t\\u1ea3ng tuy\\u1ec3n d\\u1ee5ng tr\\u1ef1c tuy\\u1ebfn h\\u00e0ng \\u0111\\u1ea7u, k\\u1ebft n\\u1ed1i Nh\\u00e0 tuy\\u1ec3n d\\u1ee5ng v\\u1edbi\\r\\n            \\u0111\\u1ed9i ng\\u0169 HR Freelancer tr\\u00ean to\\u00e0n qu\\u1ed1c.<\\/p>\\r\\n                    <p style=\\\"margin-bottom: 24px\\\">H\\u00e3y x\\u00e1c nh\\u1eadn r\\u1eb1ng b\\u1ea1n \\u0111ang s\\u1eed d\\u1ee5ng email n\\u00e0y cho t\\u00e0i kho\\u1ea3n Recland c\\u1ee7a m\\u00ecnh \\u0111\\u1ec3\\r\\n                l\\u00e0m t\\u00e0i kho\\u1ea3n \\u0111\\u0103ng nh\\u1eadp. Ch\\u00fang t\\u00f4i s\\u1ebd g\\u1eedi nh\\u1eefng th\\u00f4ng b\\u00e1o quan tr\\u1ecdng \\u0111\\u1ebfn b\\u1ea1n.<\\/p>\\r\\n            <p style=\\\"margin-bottom: 24px\\\">X\\u00e1c nh\\u1eadn \\u0111\\u1ecba ch\\u1ec9 email \\u0111\\u1ec3 nh\\u1eadn ngay <strong>200.000\\u0111<\\/strong><\\/p>\\r\\n            <p style=\\\"text-align: center;margin-bottom: 0\\\">\\r\\n                <a style=\\\"\\r\\n            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);\\r\\n            font-weight: 400;\\r\\n            font-size: 16px;\\r\\n            margin: 0 auto;\\r\\n            color: #ffffff;\\r\\n            display: block;\\r\\n            width: 300px;\\r\\n            height: 46px;\\r\\n            line-height: 46px;\\r\\n            border-radius: 8px;\\r\\n            margin-bottom: 16px;\\r\\n            text-decoration: none;\\r\\n                \\\"\\r\\n                    href=\\\"http:\\/\\/recland.local\\/verify-employer?token=4fVhqi80gPNSMJY5ucJvmjqKIxCK7ySh\\\">\\r\\n                    X\\u00e1c minh Email c\\u1ee7a B\\u1ea1n\\r\\n                <\\/a>\\r\\n            <\\/p>\\r\\n                \\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Hello, Admin Hri<\\/p>\\r\\n        <p>Congratulations on successfully creating your employer account on Recland.<\\/p>\\r\\n        <p>Welcome to the Recland Community - The leading online Recruitment Platform that connects Employers with a\\r\\n            nationwide network of freelance headhunters.<\\/p>\\r\\n                    <p style=\\\"margin-bottom: 24px\\\">Please confirm that you are using this email for your Recland account to\\r\\n                make your login account. We will send important notifications to you.<\\/p>\\r\\n            <p style=\\\"margin-bottom: 24px\\\">Confirm your email address to receive <strong>200.000\\u0111<\\/strong><\\/p>\\r\\n            <p style=\\\"text-align: center;margin-bottom: 0\\\">\\r\\n                <a style=\\\"\\r\\n            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);\\r\\n            font-weight: 400;\\r\\n            font-size: 16px;\\r\\n            margin: 0 auto;\\r\\n            color: #ffffff;\\r\\n            display: block;\\r\\n            width: 300px;\\r\\n            height: 46px;\\r\\n            line-height: 46px;\\r\\n            border-radius: 8px;\\r\\n            margin-bottom: 16px;\\r\\n            text-decoration: none;\\r\\n                \\\"\\r\\n                    href=\\\"http:\\/\\/recland.local\\/verify-employer?token=4fVhqi80gPNSMJY5ucJvmjqKIxCK7ySh\\\">\\r\\n                    Verify Your Email\\r\\n                <\\/a>\\r\\n            <\\/p>\\r\\n                \\r\\n        <p><b>Thanks and best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland Team,<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=689451a729de9);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=689451a729e06\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=689451a729e21\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=689451a729e3c\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:4:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:54:\\\"[Recland] X\\u00c1C TH\\u1ef0C T\\u00c0I KHO\\u1ea2N NH\\u00c0 TUY\\u1ec2N D\\u1ee4NG\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:2:\\\"[]\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}', '2025-08-07 14:11:35', '2025-08-07 14:11:35')
-- ","Time:":10.2} 
[2025-08-07 14:11:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` where `hash` = '8aefec82d2e3427cb027523ac4ffebe9' and `created_at` >= '2025-08-07 14:06:35'
-- ","Time:":89.13} 
[2025-08-07 14:11:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":22.82} 
[2025-08-07 14:11:36] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"from\":\"registed-success\"}', 'http://recland.local/employer?from=registed-success', 'http://recland.local/employer/register?action=register', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/register?action=register\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRlQVhVWjBVZUNzWUZVYnA0SnpmL3c9PSIsInZhbHVlIjoiZmw3cnZMU0Eyc1RDb0dIVFp3U2VBa2pYZU1uU2pBLytMcjBpUnlLVjNvVDZTU1I3ZzBPdVFlKzJJZFlVSnQrUTBPc3U2eU1ONkNVSHk2MWRhY2h4Mkl3eXFNVXJwQnhMOTNwQU1uZFQwQ1VFMlJ0bzlVc0hGNmhQQzRQVm9PVmgiLCJtYWMiOiIxZWY2NThhNDc4MDVmMGYzNTljOTQ1NjZiZWM4MDdkNDdlZmY0OTZhZDc3YTRmMjcyNThiZDI5YjI1Nzg3MmFjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkJuWEtZa3ZLc0E1WkQ4S1ljdWxUNUE9PSIsInZhbHVlIjoiME8rY1JhN0xKeS9MWUIrZkNBTlhidUs0bS8xald5THNmRjBRWkdrVmc2VExvM21WUnowYkZwZlJETnZWbEQ2MFQwZ1BRYnNZc2FxT1g0UW5WOCtxcGZWU1kxSEE1S0lTQWQ0dHZHQTBSRklJZnUrWitFOW5yeXdJVkNkaURnVUgiLCJtYWMiOiJjZWJmOWQxMGU1ZTM1NjcxODMwZDM1M2FiNDVhN2NmMmRjYTQ0MTAzYWQyZGNhYjRlNWQ0M2U3OWJkNmFhYTc5IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:11:36', '2025-08-07 14:11:36')
-- ","Time:":0.7} 
[2025-08-07 14:11:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.71} 
[2025-08-07 14:11:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.66} 
[2025-08-07 14:11:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.79} 
[2025-08-07 14:11:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.5} 
[2025-08-07 14:11:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.47} 
[2025-08-07 14:30:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":17.84} 
[2025-08-07 14:30:06] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer', 'http://recland.local/employer?from=registed-success', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer?from=registed-success\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imc5NjRxOUJFZ1VReTJKbHFpbDQwakE9PSIsInZhbHVlIjoiWlg1VFNPd0dxaW5sOENPQzlJMzNSR0pkVldNTlFLdzRZWndXZ3ZNTzNvdzkrakhFZ0pQcElmZWNiS0FOZXZ5bXhPNkJsV3MxMnhXKzdXQ2tRM2RSM3Rabjdkc0lxZWpLWmx3ekNrTld6ZmMxei8vcXpmaGNDRFZ5TWtMVzR1bGsiLCJtYWMiOiJjMzM5YTNjYjc5NjdhOTEyZThiODBhOGE1ODFmMmZiNTJjZjkwZjQ1ZTA0MWJjNzA3Y2QyNDQyMDg3Yjk0ZTUwIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjZabmNFMzg5aDdHOXczUUVMWFB2blE9PSIsInZhbHVlIjoiWUtqNkhrNFdJRVVzUkNCYTVRaFQwMmlLRnh3WEFuOG1iZlN4UlNpMW1KMW9ialVRc3BqVlc1SDFGQTZKR0pLQUtGOFZhMTI1eHlHbUorck9pQlhhby84c29mTHlpeVVtM3NUNXVla09vY0liTm1iVWNhaUorcitQdThNVlhVaS8iLCJtYWMiOiJmZDEwNTBhMzU3MjIyNDNhNjk1OTJiMDI1ZTA2OWE5Y2RjOTA3ZmU0YjQxNzdjZTYwMzU5MDc0OTQ0ZmY5MjVmIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:30:06', '2025-08-07 14:30:06')
-- ","Time:":0.87} 
[2025-08-07 14:30:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.58} 
[2025-08-07 14:30:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.71} 
[2025-08-07 14:30:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.2} 
[2025-08-07 14:30:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.38} 
[2025-08-07 14:30:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.5} 
[2025-08-07 14:30:29] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"email\":\"<EMAIL>\",\"password\":\"111111\"}', 'http://recland.local/employer/login', 'http://recland.local/employer', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"92\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IldMMlQ0MjNUbGVnV3B4cDJjeVFaMkE9PSIsInZhbHVlIjoiSmpkZGJMOWZraGtmOWo4TUV3MXRRS1NlR1FPK1ZwL2tFVEhNR2doZHFobnNmVnY0ckNmTGZZbFZuTDRkUEgyMGlDVlVuNkVRakc4K0pKZ1U3clVvZm9RUG1yRUZ4OWpiWldUSEtvRlZJdGNReVROdjZNdVAvMHdvcXFyaWJCUWkiLCJtYWMiOiIzZGI1MjAxNDZjNWY5YmMzZmFjMjZjMjkyNjZiOTVmMjI0YmFhODRmY2E1NzIxZTQ2MjJmNWUwMWM0NTY3Njg1IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ijlndmw1OFRXQlEySG4ySWRBSkl5MFE9PSIsInZhbHVlIjoiS2hGWW1YZmUyb0kybVNGQVg3ZjhVaTVCZ1FSN3VLemdLeDBxK3BrNm1XTGZzOTRMN2JOSUNaRTVKK0JlU1puQkZJWEpmUk90dit6a2ZnWWRJRWF3U1V5bG5qR3pDc3o5NHF3ZEs1ZmNGOVRPUUdZZ0VmZXowK092cXArSFJmYWsiLCJtYWMiOiI0MTM3NTQwZWRiN2JiM2Q3ODgyZDA0ZDRiY2YzYjIwNjNkMzIxODQ4YTBhNTczYzhmODRlZDNmMzIwMmNlZTk3IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:30:29', '2025-08-07 14:30:29')
-- ","Time:":15.44} 
[2025-08-07 14:30:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1
-- ","Time:":0.88} 
[2025-08-07 14:30:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' and `is_active` = '1' limit 1
-- ","Time:":0.89} 
[2025-08-07 14:30:29] local.DEBUG: [SQL EXEC] {"SQL:":"
update `users` set `last_login_at` = '2025-08-07 14:30:29', `users`.`updated_at` = '2025-08-07 14:30:29' where `id` = '7490'
-- ","Time:":1.26} 
[2025-08-07 14:30:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.65} 
[2025-08-07 14:30:29] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\"last_login_at\":\"2025-08-07 14:11:33\"}', '{\"last_login_at\":\"2025-08-07 14:30:29\"}', 'updated', '7490', 'App\\Models\\User', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/employer/login', '2025-08-07 14:30:29', '2025-08-07 14:30:29')
-- ","Time:":1.48} 
[2025-08-07 14:30:29] local.DEBUG: [SQL EXEC] {"SQL:":"
delete from `personal_access_tokens` where `personal_access_tokens`.`tokenable_type` = 'App\\Models\\User' and `personal_access_tokens`.`tokenable_id` = '7490' and `personal_access_tokens`.`tokenable_id` is not null
-- ","Time:":1.16} 
[2025-08-07 14:30:29] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('Personal Access Token', '7765ea98c1869427eef1d701fc3f9c46b74ad0cbe088cf3be7a53adb3c9ef432', '[\"*\"]', '7490', 'App\\Models\\User', '2025-08-07 14:30:29', '2025-08-07 14:30:29')
-- ","Time:":1.8} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-manager-dashboard' limit 1
-- ","Time:":13.68} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/dashboard', 'http://recland.local/employer', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik1sRzZadEM1ejQwaENpemVUN1F4TWc9PSIsInZhbHVlIjoiditpU2FjY05mVFJ4aHRpaUF6OUFlMHRqVldJMFdLeTRzSG5wQW9RUEhwOENoTExPNEt0NGdQQkV5S3JtNTV5b3Rib29WSHZ4d3Z5RmZVMzJybU9tKytSWm1zVmQrVnphOVNDQnB2aWIwaHQyUlNZUWpTSzh0eXhuRXBybVdWbVYiLCJtYWMiOiI4MjYyNzZjMTNmNDU4YWY0YzliNWQzMGUwMGYzOTJhZmI4ZTZlODM1ZDdkMWFjYmM2MmZiMzI0OTBkNWFlOTAyIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InJPcG1HUUpVbzljRFdHY0FXL0l5anc9PSIsInZhbHVlIjoiTUZlV1EwTEJQU3d6RStjRFVZYVRuNEIzYi9icXdmMlhNaEYrN2I4aHE2bEZUSVE0SGJnQjlpazdXd2FIRklOdjRvMktoOENJMGNqSnZjeDQ4cGE2b05ucFY5SGVhQ3NsZVZPRmJRUmhkSDRud0NkRVByK2FRdEhyRFY3S2tobGsiLCJtYWMiOiJmMzhjZmVkNGJhMmI0YWM3YmRhNzNkZjVhZWU2YTllNzZjYzhkMjFhMDA2NGZiOWVhNzg2MzNmOGJhYmQzZTBkIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:30:30', '2025-08-07 14:30:30')
-- ","Time:":0.4} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.45} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":24.82} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"redirect\":\"aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk\"}', 'http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk', 'http://recland.local/employer', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjZsRzNSNDBDZ3VESE0rN0JUeG85ZGc9PSIsInZhbHVlIjoiaERaTjhXOTI5bGdjVUJRUThKeFVZTzBuQUhVT1dJSEo0VHdLSmdJYnYvbThLN0FQYzh4aGVrRTNnbzBPK2J4TmUveUZZa2RIWjhzejFJRHIycVVVdml2NjVsQ1FNWkhYQmxOdGQ5SXIxTkwrcXhpYXZKWHFYQ1JQeEtDWjBkdXMiLCJtYWMiOiI1Y2Y0ZDY4MjQ5YTE1OWFhYjg5ZWZlYTY5YjM4OTM1NmZiOGU3ODI1Y2FiNjNkNWZlZWViMDNkMjE2ODU2ZWVjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkVUTnlwWjBINlJ2MXoxcXg4OEU5Snc9PSIsInZhbHVlIjoid1BUUHpUM3ozbzZjT1duNFY2ejFEK1VrWnUyYkFVV0l2ZWRJTUk2bHRCSTVVZXFDWkE1djZRWTNkM0NCdjc5bjhDR0VMT1IvbFZLUXY5N3ZLdEtCSG53dTFFM216dFZrTDVVREhOVmdvUkNtMHJPUjIxUEJ1QWxwbC82NDBSbjYiLCJtYWMiOiIwNTdjMjIxNTQ0OTk0YTMwMDg5NjA1YTNmMDQ1MTkyYWMzZTMwYzc3OGQyYjU1MjI5MzgzNDc3ODIwZTIyNDhhIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 14:30:30', '2025-08-07 14:30:30')
-- ","Time:":0.6} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.45} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.4} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.54} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.49} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.53} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.49} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.31} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.43} 
[2025-08-07 14:30:30] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.43} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":14.62} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.86} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.94} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.42} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":1.04} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.81} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.31} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":0.68} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.95} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.67} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.9} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.31} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.8} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.27} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.65} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":1.06} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.88} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":1.07} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.68} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":1.53} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.69} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.72} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":1.02} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":1.01} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.47} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.66} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.75} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.33} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.65} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.75} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.74} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.74} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.8} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.73} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":1.1} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.92} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":1.06} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.89} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.98} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.43} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.99} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.82} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.82} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":1.81} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":1.28} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":1.17} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.89} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":1.05} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":1.14} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.94} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.96} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.92} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.47} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.86} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.84} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.55} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":1.13} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.96} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.81} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.9} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.56} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":1.16} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":1.41} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":1.21} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.83} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.76} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.71} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.85} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.75} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.68} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.66} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.63} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.67} 
[2025-08-07 15:24:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.67} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":18.94} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer', 'http://recland.local/employer?from=registed-success', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer?from=registed-success\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlU1Mmc5ZVFTWmhVWExCdW9Vb293c3c9PSIsInZhbHVlIjoic1N5K0FKeTZMeTZPOEQ1ZzZvb1g3U1NnektZZWhlMC96OFVpenViaHlxMHFTVEdNOVZhOGQ0eGJNRmwzVVJRWHhyRmNwRm9tUXYwOWlLckVqTFVYMnZHNjAxWmp0dlZSK2VYeGU5b3JPK3orV1YrTklKMG5JM05HQTc5TnBCbWQiLCJtYWMiOiI3MTBlYzkwOTM3YTA4NDZhYmRiNzBmZjA3NjQwYzk5NzhmZDFjNjE5ODk4ZjAxMmZiNDNiMDM0MGI3YjE3MmQ1IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjkzdmNBaVRLODI4bXFFcWdwNkkyWVE9PSIsInZhbHVlIjoiUFpCN3plejV2U2RFOEpSM2J6VjhlbnlBOXBNQTB0TVZLQWVvSzB1aTRMOUlrSkhxMUhmR3J2bklpKzBnQnFRaEVCRTRqb3N0d00wUVhaem1Va0ROSkZzYXZsY09JL0hRZEhQR21EejV2QnVQS0JiOFgxb3B2ZHlMejVUZWlSeHYiLCJtYWMiOiJkNmNiMTM1NDU1MTQwYzViODgwOGFhNzYzZGRlODNjNjcyMWJjOTgyNmVjY2NhNTJiOTI5MDllNTlkMzJlMDY1IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:26', '2025-08-07 15:34:26')
-- ","Time:":0.58} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.67} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.43} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.48} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.51} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":2.12} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.53} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.46} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.78} 
[2025-08-07 15:34:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.75} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\",\"email\":\"<EMAIL>\",\"password\":\"111111\"}', 'http://recland.local/employer/login', 'http://recland.local/employer', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"92\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlZaVdOZ2RhdUhOZmU5OE5YQU9FNXc9PSIsInZhbHVlIjoiZitJK09kWTZUM2JuOEtpbDh4QURFK2h6R09oZER2RUpIeDFCYjB2WDR6ZUZqY1lMZFFiUndRVHM1RDNqQzVIT2FOVm5iUjFQSEhjc1BMNGpUOXo3d1BsdFhLSGxtZTJUZFBzSjYzMWdCaWV6dTBSb2UyRk13Y3JNSnR6MTNFTHAiLCJtYWMiOiI2MGRjZWE1NDAwYjdiOGMwNTZiOTAyYjJkODU0M2U2NmUwZmExNzM4YzkwY2NjYzkwOGUwYTQ4NWMzMzEwYmIwIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im40TkFoWUdZci9pOG9oeDFKR2pDbnc9PSIsInZhbHVlIjoibHMrU2hmUnB0K0RJNHRVWEV4N2liZW5NSWZuUCtYLzQvSWZYYnhyejVXWUNYK2hnaUFSSE1ZUzFVd3VlbGJqZWFTNFQzVUNNdmtDejc2WFM0d3J2Y0J4dEVaajVnUUdGYWVORklUVWxWRHJ0T1g1c2tWazJsYTg3OU05cXQ3Z3ciLCJtYWMiOiIyY2Y5NDY1NDJmMWYwOGMyZDNkNWY1ODZjMzQ0MGZmOWJlMGU3ZjUyMTIyYzgyMTkxOWIyZjcyZDI0M2ZmOGY3IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:31', '2025-08-07 15:34:31')
-- ","Time:":17.08} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1
-- ","Time:":1.0} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' and `is_active` = '1' limit 1
-- ","Time:":0.72} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
update `users` set `last_login_at` = '2025-08-07 15:34:31', `users`.`updated_at` = '2025-08-07 15:34:31' where `id` = '7490'
-- ","Time:":1.11} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.64} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\"last_login_at\":\"2025-08-07 14:30:29\"}', '{\"last_login_at\":\"2025-08-07 15:34:31\"}', 'updated', '7490', 'App\\Models\\User', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/employer/login', '2025-08-07 15:34:31', '2025-08-07 15:34:31')
-- ","Time:":0.8} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
delete from `personal_access_tokens` where `personal_access_tokens`.`tokenable_type` = 'App\\Models\\User' and `personal_access_tokens`.`tokenable_id` = '7490' and `personal_access_tokens`.`tokenable_id` is not null
-- ","Time:":0.87} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('Personal Access Token', '3948dcd49ade825eb0db9be001611514314d49d3bfbbf75fa7bd71b7d38bb61a', '[\"*\"]', '7490', 'App\\Models\\User', '2025-08-07 15:34:31', '2025-08-07 15:34:31')
-- ","Time:":1.01} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-manager-dashboard' limit 1
-- ","Time:":2.66} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/dashboard', 'http://recland.local/employer', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImtRelRnWVBqSFliOEcxOGVCVC93T1E9PSIsInZhbHVlIjoiMEhubVVJQVh1SkJtQ1pOME9wcEQ3c0VsbEpUdnJMTUR5TmZ0WS90cEhaMEt5c0Zoemp4cU9sMm10K3JqL0VDcnoxRmJGTE9wUE8wTXl6VHhGODdJNzJKelpwK3o5clV0OUMwenVqR2srVS9rWHB4WWVIaW5sVm8zSWNmQy9VVTIiLCJtYWMiOiJkNjZhZjgyZjk4ZDQxZTBiMTRhODRlMDllMjk2ZGFhNzlmYjc0N2FmODE0ZDZkZGJmM2JlNDUzN2M3NzI5ZDg0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjZFZG1FQzF6VlV3U1hCMmlUWHBlVWc9PSIsInZhbHVlIjoiRUZocE5Ga3hlT2dOdllUSXVIdnhxZzlEVlppamxxaXYrRDJMeWx6cFFlUUMzQ3FlRUVNRUNaVDdidUUrSmxoYTcraUpzTTQrOVdLZVRjTXU0YzRGZnBKV3ZLbFo2bytDaG9mSis5RHZmcGZtd1d3eFp4M3ltUGM1WGsrMjMvZ1ciLCJtYWMiOiJkZDUxOWY0MTUyNjExNmE0MzlmYjc3MDYwYmYyZTg4ODFiMzgwM2NmNjlkNjUwMGE1NDJjNjRhNWMzYjA0NWI1IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:31', '2025-08-07 15:34:31')
-- ","Time:":0.41} 
[2025-08-07 15:34:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.44} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":21.73} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"redirect\":\"aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk\"}', 'http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk', 'http://recland.local/employer', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Img0eWpWNGw1dit4ZlNwVitYZmwzdGc9PSIsInZhbHVlIjoiMUVUYVVkNXFMakhhY3p5d3JDV3F3TFdxMDVMWWh0Mm04SEpUVEhBYkx1VXJwQUFHY3c2U21scXdyVStYYW13UkdHdUxEaU9rdGxacWpxUitEdGhZWmI1cHZBelZnR1lMeWdEYXAvZnp6MjdrY2tVT0FlYmJPYzE5RU13MWJPcnMiLCJtYWMiOiI5OGQyZDA5ZmE4NmFhZDNiYzdjMDA5ODkzN2Q3OGI5NWU3ZDFiNmEyNjI0OTQ2ODQyZGViYmVhMmZmMjU4ZWIxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImtjcWw4MmFrVkV4SnQzQmlxMmVhK1E9PSIsInZhbHVlIjoiS1RjRFphZ0Z4ZGN2OXIzU3dtbVpBK2QyRXJxdGNkUVZacm1KK3BoSkU1bHVNc1dGeStSTGRZSmZXRUFMOHZ1dDBzcFlnL0tKajNhbEhmSmxGOVVKdHRGb3N2dnU2dUhpZ3ZyVVkrYmZNTjJpaUxReWoxSVczMHJYNi9HNzllWFMiLCJtYWMiOiJmODUyN2UwZTM1ZWVhMjNmYTRlNGMwNjI2NGZiYzA0YjA0ZDA2NzIwYTk2Yjk2MGFiYmUzM2IyNmU3MWVkNGE5IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:32', '2025-08-07 15:34:32')
-- ","Time:":0.58} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.61} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.3} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.46} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.48} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.96} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.58} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.43} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.83} 
[2025-08-07 15:34:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.75} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-manager-dashboard' limit 1
-- ","Time:":21.98} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/dashboard', 'http://recland.local/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer?redirect=aHR0cDovL3JlY2xhbmQubG9jYWwvZW1wbG95ZXIvZGFzaGJvYXJk\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1PNUd3UFhlNU5QVUxtaUN3c1U3OXc9PSIsInZhbHVlIjoiMkpPWEhPMi9FTXJMem9Lbk9QUXVqZXQ3L1FicE9DUWcvY2dnVjFEdXNjWDlma0hTbU9CZStub2FOMU5VVFRjeUJwUWJZOVk4VkdTeExza2dPVUtPWDlrMlB3SkZXMkh4K1IwY3RXeWdYUTJOVllYd0hIUUJqb2JIQ0l3eDJNUGQiLCJtYWMiOiJjMGM0ZmJmOWQ4MjQzNjRiNjJjZTkxMzY4OTI2NjdiZGJlNzJmZTQ1ZDVhZGU3MTcxMTRiNGVlYWQ4YjA3ODY5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IndmbEhIUHkvc2xlcWxTcTJ0VUJBc2c9PSIsInZhbHVlIjoiRXlQeVdQR2FTMmNXbXE4UmlvZ2plcitMbWU1U1Nmc3dUWERXZGdVcllvMEQzU2h0V3FiRzJOYzJOVFRVUDFXcUZ1TTBreG9lS2JxZzZBbWd0L3lkVkJQVnQ0WDN0bncwT2ErZDliZHlQNG1qa05HdnlNK3JGeS92SDRzWlJ1dXMiLCJtYWMiOiI5MzkwOWUzNDEzZWRkZWY3NTI2ZDdhNDE2YmUwOWVkNWExOGNhOTQ0ZWU3Mjk4MGZkYWFjZDk1ZmEzNzEzMzUwIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:34:55', '2025-08-07 15:34:55')
-- ","Time:":0.6} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.66} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.79} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.32} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '7490'
-- ","Time:":8.02} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `status` = '5' and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1'
-- ","Time:":17.76} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `status` != '7' and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1'
-- ","Time:":31.88} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '0' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":2.17} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '1' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":2.02} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '2' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":2.24} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '3' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.99} 
[2025-08-07 15:34:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '4' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.87} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '8' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.86} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '5' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":2.02} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1' and `status` = '6' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.88} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-manager-dashboard' limit 1
-- ","Time:":0.33} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '7490') and `is_active` = '1'
-- ","Time:":23.1} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `user_id` = '7490' limit 1
-- ","Time:":2.3} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `deposits` where `user_id` = '7490'
-- ","Time:":0.26} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select sum(`amount`) as aggregate from `deposits` where `user_id` = '7490' and `created_at` between '2025-08-01 00:00:00' and '2025-08-31 23:59:59'
-- ","Time:":0.28} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select sum(`amount`) as aggregate from `deposits` where `user_id` = '7490' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59'
-- ","Time:":0.28} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '7490'
-- ","Time:":0.27} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '7490' and `type` = '0' and year(`created_at`) = '2025' and month(`created_at`) = '08'
-- ","Time:":0.29} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '7490' and `type` = '0' and year(`created_at`) = '2025'
-- ","Time:":0.27} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.43} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.84} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.33} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:34:56] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.43} 
[2025-08-07 15:35:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'frontend.auth.employer.confirm' limit 1
-- ","Time:":26.27} 
[2025-08-07 15:35:00] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"confirm\":\"1\",\"_token\":\"BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb\"}', 'http://recland.local/employer/users/confirm-policy', 'http://recland.local/employer/dashboard', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"57\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/dashboard\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJVcVZnWVkvMHY5RUl4OG1vU0VHTHc9PSIsInZhbHVlIjoiTnFSbTVTTFBXdk1IcktYSmx4M0RSU2ZEQUpNU1N1TE1EUERIaGcyNlR5eFNWeFB3c1NwV2tzT3VqbGl6OXlrdmx2SlJOTTc4K2hNWTQyUnVMVDlUaHlLSzNZeDcrallHNVBNcTlTejVqK2dvVUlGaXh2Q1Y5V1RONWJwVDdrT0ciLCJtYWMiOiI0MTE0MzZkNTQ5YzY1MzhiMWY0MTBkYjlmZTk0ZmExZTljNTFjZmVmNzFhZDU0YTdhZTA0ZGU4ODkwMzcwMWRjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjZJT3k3SUpBbnVpZEVtUmFYVDVMSEE9PSIsInZhbHVlIjoiNWRKRXVJblhVczZNZkExc2NyaWN5SFU4OVlQbHNCeEUzQ2R4ek9TVkN3UmRIVkI5bEIwRDVLQTRCNndxSWxwVUVsUlFLZTNYQ0R2WTE3MStFWkJraFRHUUtKZ05MTlhuSXlseHZWWWRRRyt6VU1qVHM0VWkrQm05ZktHVk03d08iLCJtYWMiOiI3YWFhYzk0MTRjYmU5YjMxNzQ0MTgzOWNjMmIwMDFmODdmODI2YWJhODBhMTQ0MTQzNGYwM2U3MGRiODBlNTg4IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:35:00', '2025-08-07 15:35:00')
-- ","Time:":0.81} 
[2025-08-07 15:35:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.63} 
[2025-08-07 15:35:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.79} 
[2025-08-07 15:35:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-07 15:35:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.35} 
[2025-08-07 15:35:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.76} 
[2025-08-07 15:35:00] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `meta_data` (`key`, `value`, `object_id`, `object_type`, `updated_at`, `created_at`) values ('employer_confirmed_at', '2025-08-07 15:35:00', '7490', 'App\\Models\\User', '2025-08-07 15:35:00', '2025-08-07 15:35:00')
-- ","Time:":9.23} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-company-profile' limit 1
-- ","Time:":27.87} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/company-profile', 'http://recland.local/employer/dashboard', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/dashboard\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndsQnRwR1JuTVo0NHQzbW5HQTFEOVE9PSIsInZhbHVlIjoidDdlZFJzUjdLMStNYWtiQVVWR0xQWUExRndlOE5NUjNjTXRucCtWbXJaUU1wQnp5VTRRUTlkRXErUnplWENnZGVtL3ZoSkg4TUFtV3FBTVJKMTlPNVV1UDVsbHFSZGdGZW03VHVWV2MzOUlEUTJUMlhPS0lIeHppYUNrY3JWdmwiLCJtYWMiOiJlOGFjY2U0Njc1N2M5YjIyNzgyOTY2MTE1NmRhM2UxYWEyZDgzY2QzMGEzNzk1Zjg4MGZmYmJlYTA2NDg3MWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImR4MGRIWVdVTG03Z1ZDRVlXc3k5OGc9PSIsInZhbHVlIjoiZ21RNjhVZ2JMc0pONXMweVlRbDRHVXA0SlVkeGM3dWR6REhhRUE0bzE5ZmlPV1BhaThHRi82Tm5JbWlRczloa2lxQ0RESE1ob2VubklBV0ZFVHU5RmF2L1c5eG5mWDBSQnhCak1wRUgzZ0VZa2pRcjdLWmY1N0pSeTJCaWE3TGIiLCJtYWMiOiIwMWJkOTYzMWZhNmYzN2FlNjI5YWQyZTllOWMzMDk4NWMwZmQ2MTkwMmFmOTY3ZTI0MjhkMjAxZGU0N2FhNTkxIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:35:02', '2025-08-07 15:35:02')
-- ","Time:":0.61} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.64} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.77} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.33} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.34} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-company-profile' limit 1
-- ","Time:":0.46} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.76} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.24} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '698' and `companies`.`id` is not null limit 1
-- ","Time:":0.39} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":1.09} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.83} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.86} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.76} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.81} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.76} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.73} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.75} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.65} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.59} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.43} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:35:02] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-company-profile' limit 1
-- ","Time:":27.11} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/company-profile', '', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjgvU0hMTEpmUWFZUGZJMjBjV2xrQVE9PSIsInZhbHVlIjoiQ29udnZzNTdmMDQxR3Q1WDBBRFBiWDNzRGxFdE55S0IxV09vTlZ3NHIrZFZpZCtKd3VMb202YUhpdzE0dlVYTWJSQXlTdUNFVitYOFY3MHF5MHVKSGl2WmxOY1huZzRhb3hhR25pMzJJZWM0R2NMMFZkZGNsN1RCNGFIMnVZWU4iLCJtYWMiOiJiNTEzMDRmNmI1NTQ5NTMyYzdhMWZhMDY0ZjhkZmIzZTFiNTAxYjA2MDUyNWY1MzM3ZTE2ZDdkNmM0YWExYjU4IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjJVdHdXaEJyWmRwMHJlZEVIRU0ycUE9PSIsInZhbHVlIjoiSXV1a3Fxd0JzNlh3dGtNMTd6SWN0V1lDbHlORVNrdFl4NmhCbmhXQXdSK0p5U3pyTVQzVlhTWEEveVczVzJ2WlRIbkdQSVhIZ3JseCswYmp6d1BKK2JJcldrWURTSFFQcTZSbmJSR2JMZDNMVXNsc2NZanhkK1V4YVZVTDBIWE0iLCJtYWMiOiI0NzQzNmY2MzA5MzEzOWJmZmIxZDRkYjRlNmIzODk5NDc5MDkxMjAwMjZkNjBjZDU0YzkxMmEyNzZjZjBlODhhIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:35:14', '2025-08-07 15:35:14')
-- ","Time:":0.6} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.66} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.81} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.36} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-company-profile' limit 1
-- ","Time:":0.46} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.91} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.29} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '698' and `companies`.`id` is not null limit 1
-- ","Time:":0.37} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.48} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.47} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.47} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.39} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.37} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.48} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.47} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.47} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:35:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":17.57} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job', 'http://recland.local/employer/company-profile', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/company-profile\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InVGWEpzaGp1ZmxaN1BIVnBYbjBxUUE9PSIsInZhbHVlIjoiNVpjYStGbm1xSis5RGUxdDE4RVl5Sll3RC91WHRGQlRlUzJxbW5rc0JaMWp2U0hLZkJkb1JWYjI2bk5JRmF3dTZqUHE1MU44ZXV1OU5sMGdBcVpsc0FlMDgwckJOZDIwbjloVE45R1A3TnlrTlpnd1orS1B3SkQvU1J6cmhYU0MiLCJtYWMiOiI0N2M0MDk0YzBmMzY0NzY3YjBmZmExYTA5NzM2ZWJjNWZjMzE2N2ZkMDU3ZTc1ZmI4NTkyOTkwNWM0ZjMxYzVlIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkQvdXl1R2hNZzFQVklGTlVERFNjd3c9PSIsInZhbHVlIjoiS2h5V2xxZUdUa2lvMG5qa21pVmZ1T0xCeVIyQmNXT0s5ZnpvNUh1ZlpkOE1zUXpYbi90RWlMZzV5eHExeEVqYmVLSUU4d28zQ2FBNnBQYXA0RWYxcG4vaHpFNzVPRmJPdnd0ZmNkMnBMVzdXamgvdldOeSs5WnFOcXVqTXFWN00iLCJtYWMiOiJjOWViNGE1ZDVkYTc2MmFkNmMwNzQzN2NhMmQ4NWZhMGJkNTE3MjJhNGI4NjQxNWY1N2ExZWY2ODIxMzIwZDgxIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:36:17', '2025-08-07 15:36:17')
-- ","Time:":0.57} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.57} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.75} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.6} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":0.42} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `employer_id` = '7490' and `company_id` = '698'
-- ","Time:":5.48} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '7490'
-- ","Time:":4.21} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '0' and `employer_id` = '7490'
-- ","Time:":4.08} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.5} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.5} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.48} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.4} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.37} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.47} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.48} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":15.06} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ing1czYwUVh1WGtUcVVZUHdBMzRaNnc9PSIsInZhbHVlIjoiWFB4NFl3Z1JLeXdxVEtDelRxUjR3NlZHTVhmSjNXVXpsckFWekNuRTBMSm4xaTVRY2RMK2lFaFo4L05kYUJYVG5TQ1JjWG1IV3NFL0VlZ2Vva1p4T3MyK2M4UGkxTmNrNjNaSWJwazluNzZaMkh1cEdNcnBycmZhRlEydlhnNGYiLCJtYWMiOiI0NTU2ZWYxYmE4MjM2YzVjNDdmYzhkODNhZDJiNDdlMWU4OTZjODg0NzRkOGE5YzBhNjJhMjI4MzE2YzFlZDc0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IldQZkw4bDJIanNzeC8xUlRhVGpORXc9PSIsInZhbHVlIjoiTkVORm1zWVp3NWRXZGpxVjB4M3pOT2FDeXpqK1BjZWVaa0ZZb05ZZHNZMlBadHppd3hlekQwbC9NQ250L2RqOHl5ajYvRTFQT0kraHVsMGFZVmdkTFFmZGdqQnI5QnNCbVJrR0swNHNzNkNvK3pTdHBLaHpaVnU2MTlqYUN2SXAiLCJtYWMiOiJiNTEyNmExZGI0ZmVhZTNkNDhkYWIzZTM2ODMzOWU3ZTc4NzgwOWUxOTZjZmNhOGYxYjg1YzIxM2Y5ZGY5ZmFkIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:36:19', '2025-08-07 15:36:19')
-- ","Time:":0.41} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.69} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.8} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.33} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.51} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.51} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":7.99} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '698' and `companies`.`id` is not null limit 1
-- ","Time:":0.4} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.51} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.5} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.48} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.47} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.47} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.47} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.42} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.39} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.48} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.51} 
[2025-08-07 15:36:19] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.48} 
[2025-08-07 15:36:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":24.79} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkVFd212Q2dFcnJVZEJmcnJPa0NObFE9PSIsInZhbHVlIjoicHVKNmdUdEw1ZStHMS9VNGZraEpBemRLUUdiRmdKRnFaaC9sV0YzVUFscmVpVWROL00rT1RBZXlyU1YycUlDMUhKdksxRzRYclNOU2E2aHhVZTdpZjBuNTA0cjFhUm14YW5TN3JSYmtvemQ4eDhVcURpR0Z6MHh4ZWEzSURmc0YiLCJtYWMiOiJjY2M0OWQ4Mjk2MGY4ODMxYmYxMTAyMzBiMWNkYmZjNzZhZjM3NmU4NzI1YTdkOTRiYTQwOTcxOGMwM2Q1MWVhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ims3dEQ2TzdCOFBLNUwxOG1oSFBuaUE9PSIsInZhbHVlIjoiRXprbExXWWg0enZlalY2eFVoRzJVRUFvUzJqd1pDZHZaVmpTb05HZGkya01BR0EyaVJlZlk1L2hjM3dqR1B3TGZFTTZUZkd0eWF3UVBweURUWTBlSTMwSFJKV1BhRy9YTUk4bHVUQUZzMjFVaEhQNlN1ejIvV0lyczk5SWNWS3ciLCJtYWMiOiJiOTIxMzU5MDhlNDE5MWY5ZGMzZmVlNWU1MmQzNWY1ZDU1ZTY3NDI5NjFmMDE2ZTZiNWQwZjQzOTk5OGJmZmMwIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:36:59', '2025-08-07 15:36:59')
-- ","Time:":0.58} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.63} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.76} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":3.21} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.35} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.34} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '698' and `companies`.`id` is not null limit 1
-- ","Time:":0.42} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.51} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.48} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.5} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.51} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.41} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.41} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.51} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.5} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.48} 
[2025-08-07 15:36:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.52} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":26.18} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJVMndTdGFmelo5Q01GTmR3NGM4SEE9PSIsInZhbHVlIjoiZGQ5T0VNM2pGdmk2UFRhUlRXdG90cTMrbTloNThFcDkvMnFSaEdnSG5mN3YyU2wwYVd4OWpqWGpmeEFLSElKb3Z0dTR2OXNIcVFLWk1NQzFTSUlEL0V2VGtqbTUxU0xlbjJJZnZ3aWI5RnJHWGdXL2U4bmh4VmczSTNsUGYvK20iLCJtYWMiOiIzNWIyODdiMmFhMTVkMmFhYmU2ZjBkMDRkY2VjMzBkZDFmZDYxNzg1MDU3NzNmMjZhZDE0OGQwNjViMjA3MmQyIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImVNbGxCa0FWRDJtMTc4QmRxV0pVR1E9PSIsInZhbHVlIjoiYXFUeDlub3BQQ3pXazdkS1RmN2tROVpYRUM3TUNUamQ3SjRkajZ1LzR5UlRwTmVjQm9uSGh2T2dyZ1hxclFicTJDaWNSUWJ3MWdZRnpITW5rV24xcHFvYmJxemo1TENkWGZMcVFEWXdjRGdoZ3E3TE1XM1Zab015QW84RVYwckIiLCJtYWMiOiI3ZDAzMmVmNzRkM2ZmYjViMTQ0ZDhhOGIzMDE5OTg3YWUyZjYxODU2MjFiODQ4N2NiNmQ3MWE5MGUxODA4YjFkIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:37:18', '2025-08-07 15:37:18')
-- ","Time:":0.62} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.67} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.8} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.33} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.25} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":0.43} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `employer_id` = '7490' and `company_id` = '698'
-- ","Time:":4.62} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '7490'
-- ","Time:":4.19} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '0' and `employer_id` = '7490'
-- ","Time:":3.95} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.49} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.46} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.39} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.36} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.45} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.44} 
[2025-08-07 15:37:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.43} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":2.79} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjNBRjdHeVIvTVNEdEpFZElPSGtxTEE9PSIsInZhbHVlIjoiUGF1VnorYW5abmdoWW1UNUc3RzdXc3FqMmk5SnZ3UUMrZHNFUWwrNE5teEdMSmR5aUlmU3Z5RWZVN3RGUXRWcW56am9PekdwV2dTczY1N1JNbjJIcGZxTjhERGxwRkZaNjhCMVRvNWs1Qlk4SHN0V3FGdmY1SEVJeHFPOVQvUG8iLCJtYWMiOiJhOTA1NmQ2NTY5OGFiMmFmNzI2MzFkMDdjNmEwY2NmMTY2NDhjYWUwMWYyNDU2MWIwMTQ5ZDlhMzU5ZWNiMmM2IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImxjQlNBV21wQ3J6ZXhob1labXV4THc9PSIsInZhbHVlIjoibTkyQ2ZLWGRUdDZsYVdTSjFGaU4wWURBWmRORjRqYnI0Q0lVSjN2eFNndUFLa0NzUFJDUFBrem5oZThNYnJkT1Mzb2NpMlVIUEcyVmNMb3IxNDA4Q0VhYW14NUdiT29rdzY3cnZCTnpoVWplUk14dTcvYi84aFZsbGkvU3Z5RXciLCJtYWMiOiIwM2UyYWMxNGYyYmI0ZDI5ZWM4OGIyZjZkZjhjNTFlZjhmYTZiMGZkZGNiMzc4OGViYjZkYjU1MDlmOWVkNDBiIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:37:36', '2025-08-07 15:37:36')
-- ","Time:":0.39} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.51} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.5} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.3} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.36} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.32} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.37} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '698' and `companies`.`id` is not null limit 1
-- ","Time:":0.35} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.42} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.42} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.42} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.39} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.34} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.47} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.43} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.41} 
[2025-08-07 15:37:36] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.46} 
[2025-08-07 15:40:12] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-company-profile' limit 1
-- ","Time:":23.38} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/company-profile', 'http://recland.local/employer/job/create', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IktmZEJyWUhKaVYyRDZCRmRzL0YwL2c9PSIsInZhbHVlIjoid05RbVBsTW5hdFVoekQwWnhYeE0ra1RvRWcwc3hoZks1QmRwWU9VaGZOd3dmTGRDVGViNlg5a0Z5emxPZHlxUDBmbWdIQlc5VTVwTnFvV1VYZXIwRUFVLzdGRzhXU3pORTlLMXBXTWRnZGZWdUFsWUp4UzdwR1l6TDMyZllkTHAiLCJtYWMiOiJjMDBhYWUxNWVlNTRkYjRlOWFkMzFjOTE2YjY5ODdiMGNhYTMzM2UyYjAyNTU5NTY3YmJlNWRjMTMxZDU4NjZhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkFhbXBRdEV2TkpSRTF6czY3ckhxZWc9PSIsInZhbHVlIjoiZzFnanV2cmpXeHFpMThGTjJ2S0FBdmdKOE5YWFQ3c2NGcXRsSU9jUGNwTjV0Q3BGdlZsSi96N3E3Q0VLaVJEYUdZUXFFanJrZzhzZW9ISk8vY3hIc1gzZ0hBOHZVQTV1MHdSUzZOMmFQaDNhNW4vQW84WTBZT09TT3dTZDJsU2IiLCJtYWMiOiIxNTc3ZWQ0YjIxZTIyYjNiOTc4OGU0NzdjNWZhMzE5YTdhNmI4N2E2YjE4YmZjZGJmYzEyOTEzMzY3N2M2NDBiIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:40:13', '2025-08-07 15:40:13')
-- ","Time:":0.59} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7490' limit 1
-- ","Time:":0.41} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.42} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.39} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.27} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-company-profile' limit 1
-- ","Time:":0.45} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.72} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.3} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '698' and `companies`.`id` is not null limit 1
-- ","Time:":0.49} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.4} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.37} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.32} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.4} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.39} 
[2025-08-07 15:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (7490)
-- ","Time:":0.38} 

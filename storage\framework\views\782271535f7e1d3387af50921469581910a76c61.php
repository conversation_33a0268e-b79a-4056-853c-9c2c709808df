
<?php $__env->startSection('content-collaborator'); ?>
<div id="page-user-profile">
    <div class="header-tab">
        <ul class="nav nav-pills header-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <a href="javascript:void(0)"
                    class="item-header-tab from-storage <?php if(Session::has('action') && Session::get('action') == 'change-user'): ?> active <?php endif; ?>"
                    data-bs-toggle="pill" data-bs-target="#content-tab-user">
                    <span class="icon icon-company"></span><span><?php echo $arrLang['thongtinntd']; ?></span>
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a href="javascript:void(0)"
                    class="item-header-tab from-storage <?php if(!Session::has('action')): ?> active <?php endif; ?>"
                    data-bs-toggle="pill" data-bs-target="#content-tab-user-profile">
                    <span class="icon icon-company"></span><span><?php echo $arrLang['thongtincongty']; ?></span>
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a href="javascript:void(0)"
                    class="item-header-tab new <?php if(Session::has('action') && Session::get('action') == 'change-password'): ?> active <?php endif; ?>"
                    data-bs-toggle="pill" data-bs-target="#content-tab-password">
                    <span class="icon icon-change-password"></span><span><?php echo $arrLang['doimatkhau']; ?></span>
                </a>
            </li>
        </ul>
    </div>
    <div class="header-company-info" style="display: none">
        <a id="back-to-company" href="">
            <img src="<?php echo e(asset2('frontend/asset/images/dashboard-ctv/arrow-back.svg')); ?>">
        </a>
        <?php echo e($arrLang['chinhsuahosocongty']); ?>

    </div>
    <div class="tab-content">
        <div class="tab-pane fade <?php if(!Session::has('action')): ?>  show active  <?php endif; ?>" id="content-tab-user-profile">
            <div class="main-form">
                <div class="wapper-form">
                    <form method="post" id="form-company-info" action="<?php echo e(route('employer-change-info-company')); ?>"
                        enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="title-group">
                            <?php echo $arrLang['hosocongty']; ?>

                            <?php if($flg['thongtin_congty'] == 1): ?>
                            <a data-toggle="edit-form" class="button-edit" role="button"><img
                                    src="<?php echo e(asset2('frontend/asset/images/dashboard-ctv/icon-edit.svg')); ?>"></a>
                            <?php endif; ?>
                        </div>
                        <?php if($company): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="row row-form">
                                    <div class="col-md-12 item-col-form">
                                        <div class="group-item-field">
                                            <label><?php echo $arrLang['tenhienthi']; ?> <span>*</span></label>
                                            <div class="item-form">
                                                <input disabled class="item-field" value="<?php echo e($company->name); ?>"
                                                    name="name">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12 item-col-form">
                                        <div class="group-item-field">
                                            <label><?php echo $arrLang['masothue']; ?> <span>*</span></label>
                                            <div class="item-form">
                                                <input disabled class="item-field" value="<?php echo e($company->mst); ?>" name="mst">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12 item-col-form">
                                        <div class="group-item-field">
                                            <label><?php echo $arrLang['webcongty']; ?></label>
                                            <div class="item-form">
                                                <input disabled class="item-field" value="<?php echo e($company->website); ?>"
                                                    name="website">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12 item-col-form">
                                        <div class="group-item-field">
                                            <label><?php echo $arrLang['quymo']; ?> <span>*</span></label>
                                            <div class="item-form">
                                                 
                                                <select disabled style="width: 100%" class="select2-custom"
                                                    name="scale">
                                                    <option value="">Chọn</option>
                                                    <?php $__currentLoopData = config('constant.scale'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $scale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($key); ?>" <?php echo e($company->scale == $key ? 'selected' :
                                                        ''); ?>><?php echo e($scale); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <?php
                                                $arrCareer = explode(',', $company->career);
                                                $arrCareer = array_flip($arrCareer);
                                                ?>
                                    <div class="col-md-12 item-col-form">
                                        <div class="group-item-field">
                                            <label><?php echo $arrLang['nganhnghe']; ?> <span>*</span></label>
                                            <div class="item-form multiple-select2">
                                                <select disabled id="career-select" style="width:100%;display: none"
                                                    class="select2-custom select-max-item" multiple name="career[]">
                                                    <?php $__currentLoopData = $career; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($key); ?>" <?php echo e(isset($arrCareer[$key]) ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="group-item-field">
                                            <label><?php echo $arrLang['diadiem']; ?> <span>*</span></label>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="group-item-field">
                                            <label><?php echo $arrLang['diachicuthe']; ?><span>*</span></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="group-field-repeater">
                                    <?php for($i = 0;$i < 3;$i++): ?> <div class="row">
                                        <div class="col-md-3">
                                            <div class="group-item-field item-col-form">
                                                <select disabled style="width: 100%" class="select2-custom"
                                                    name="address[<?php echo e($i); ?>][area]">
                                                    <option value=""><?php echo $arrLang['khuvuc']; ?></option>
                                                    <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option <?php if(isset($company->address_value[$i]) &&
                                                        $company->address_value[$i]->area == $key): ?> selected
                                                        <?php endif; ?> value="<?php echo e($key); ?>"><?php echo e($city); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-9 item-col-form">
                                            <div class="group-item-field">
                                                <div class="item-form">
                                                    <input disabled="" class="item-field"
                                                        name="address[<?php echo e($i); ?>][address]"
                                                        value="<?php echo e(isset($company->address_value[$i]) && $company->address_value[$i]->address ? $company->address_value[$i]->address : ''); ?>">
                                                </div>
                                            </div>
                                        </div>
                                </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                </div>
                
                    
                        
                            
                                
                                    
                                    
                                        
                                        
                                    
                                

                            
                                
                                    
                                    
                                        
                                        
                                    
                                

                            
                        

                    

                <div class="col-md-12 col-form-item">
                    <div class="group-item-field item-col-form">
                        <label><?php echo $arrLang['gioithieu']; ?></label>
                        <div class="ck-custom">
                            <textarea readonly id="editor1" rows="10" name="about"
                                cols="80"><?php echo e($company->about); ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-4 item-col-form">
                                <div class="group-item-field">
                                    <label><?php echo $arrLang['logo']; ?> </label>
                                    <div class="image-item-field upload-image upload-logo">
                                        <img src="<?php echo e($company->path_logo); ?>">
                                    </div>
                                    <input type="file" name="logo" hidden>
                                </div>
                            </div>

                            <div class="col-md-4 item-col-form">
                                <div class="group-item-field">
                                    <label><?php echo $arrLang['banner']; ?></label>
                                    <div class="image-item-field upload-image upload-banner">
                                        <img src="<?php echo e($company->path_banner); ?>">
                                    </div>
                                    <input type="file" name="banner" hidden>
                                </div>
                            </div>

                            
                                
                                    
                                    
                                    
                                        
                                        
                                        
                                        
                                        
                                        
                                    
                                
                            <div class="col-md-4 item-col-form">
                                <div class="group-item-field " style="position: relative">
                                    <label><?php echo $arrLang['uploaddkkd']; ?> </label>
                                    <input class="form-control" type="file" name="business_registration_number">
                                    <div class="upload-business-registration"
                                        style="position: absolute; top: 40px; right: 9px">
                                        <?php if($company->path_business_registration_number): ?>
                                        <?php if(pathinfo($company->path_business_registration_number, PATHINFO_EXTENSION)
                                        === 'pdf'): ?>
                                        <a style="width: 100px; background-color: #196a80" class="badge rounded-pill"
                                            href="<?php echo e($company->path_business_registration_number); ?>"
                                            target="_blank">PDF</a>
                                        <?php else: ?>
                                        <a style="width: 100px; background-color: #196a80" class="badge rounded-pill"
                                            href="<?php echo e($company->path_business_registration_number); ?>"
                                            target="_blank">Image</a>
                                        <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="group-gallery">
                        <label><?php echo e($arrLang['hinhanhtoida']); ?></label>
                        <div class="row-gallery">
                            <?php if(count($company->list_image)): ?>
                            <?php $__currentLoopData = $company->list_image; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="frame">
                                <div class="col-gallery add-item-gallery add-item-image item-gallery-<?php echo e($k); ?>"
                                    data-index="<?php echo e($k); ?>">
                                    <img id="item-gallery-<?php echo e($k); ?>" src="<?php echo e($item); ?>">
                                </div>
                                <span class="btn-x" role="button"></span>
                                <input type="text" name="image[<?php echo e($k); ?>]" value="<?php echo e($company->image_value[$k]); ?>" hidden>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if(count($company->list_image) <= 5): ?> <div class="frame">
                                <div class="col-gallery add-item-gallery add-item-image item-gallery-<?php echo e(count($company->list_image)); ?>"
                                    data-index="<?php echo e(count($company->list_image)); ?>">

                                </div>
                                <input type="file" name="image[<?php echo e(count($company->list_image)); ?>]" hidden>
                        </div>
                        <?php endif; ?>
                        <?php else: ?>
                        <div class="frame">
                            <div class="col-gallery add-item-gallery add-item-image item-gallery-0" data-index="0">

                            </div>
                            <input type="file" name="image[0]" hidden>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="group-gallery">
                    <label><?php echo e($arrLang['videotoida']); ?></label>
                    <div class="row-gallery">
                        <?php if(count($company->video_value)): ?>
                        <?php $__currentLoopData = $company->video_value; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="frame">
                            <div class="col-gallery add-item-gallery add-item-video item-video-<?php echo e($k); ?>"
                                data-index="<?php echo e($k); ?>">
                                <iframe width="158" height="88" src="https://www.youtube.com/embed/<?php echo e($v); ?>"></iframe>
                            </div>
                            <span class="btn-y" role="button"></span>
                            <input type="text" name="video[<?php echo e($k); ?>]" value="<?php echo e($v); ?>" hidden>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if(count($company->video_value) <= 5): ?> <div class="frame">
                            <div class="col-gallery add-item-gallery add-item-video item-video-<?php echo e(count($company->video_value)); ?>"
                                data-index="<?php echo e(count($company->video_value)); ?>">

                            </div>
                            <input type="text" name="video[<?php echo e(count($company->video_value)); ?>]" hidden>
                    </div>
                    <?php endif; ?>
                    <?php else: ?>
                    <div class="frame">
                        <div class="col-gallery add-item-gallery add-item-video item-video-0" data-index="0">

                        </div>
                        <input type="text" name="video[0]" hidden>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="bottom-form" style="display: none;">
            <div class="item-button-form">
                <a href="" class="destroy-form item-button"><?php echo config('settings.' . app()->getLocale() .
                    '.rec_profile.huy'); ?></a>
            </div>
            <div class="item-button-form">
                <button class="save-form item-button"><?php echo config('settings.' . app()->getLocale() . '.rec_profile.luu'); ?></button>
            </div>
        </div>
        <?php endif; ?>
        </form>
    </div>
</div>
</div>
<div class="tab-pane fade <?php if(Session::has('action') && Session::get('action') == 'change-password'): ?> show active <?php endif; ?>"
    id="content-tab-password">
    <div class="main-form">
        <form method="post" id="form-change-password" action="<?php echo e(route('employer-change-password')); ?>">
            <?php echo csrf_field(); ?>
            <div class="form-change-password">
                <div class="row item-form-change-password">
                    <div class="col-md-4">
                        <label><?php echo $arrLang['matkhauhientai']; ?></label>
                    </div>
                    <div class="col-md-8">
                        <div class="group-item-field field-item-st1 field-password">
                            <div class="item-form">
                                <input class="item-field" type="password" name="current_password" id="current_password"
                                    placeholder="" autocomplete="off">
                                <?php if($errors->has('current_password')): ?>
                                <label id="current_password-error" class="error"
                                    for="current_password"><?php echo e($errors->first('current_password')); ?></label>
                                <?php endif; ?>
                                <span class="toggle-password show-password" data-toggle="toggle-password"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row item-form-change-password">
                    <div class="col-md-4">
                        <label><?php echo $arrLang['matkhaumoi']; ?></label>
                    </div>
                    <div class="col-md-8">
                        <div class="group-item-field field-item-st1 field-password">
                            <div class="item-form">
                                <input class="item-field" type="password" name="password" placeholder=""
                                    autocomplete="off">
                                <?php if($errors->has('password')): ?>
                                <label id="password-error" class="error"
                                    for="password"><?php echo e($errors->first('password')); ?></label>
                                <?php endif; ?>
                                <span class="toggle-password show-password" data-toggle="toggle-password"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row item-form-change-password">
                    <div class="col-md-4">
                        <label><?php echo $arrLang['nhaplai']; ?></label>
                    </div>
                    <div class="col-md-8">
                        <div class="group-item-field field-item-st1 field-password">
                            <div class="item-form">
                                <input class="item-field" type="password" name="confirm_password" placeholder=""
                                    autocomplete="off">
                                <?php if($errors->has('confirm_password')): ?>
                                <label id="confirm_password-error" class="error"
                                    for="confirm_password"><?php echo e($errors->first('confirm_password')); ?></label>
                                <?php endif; ?>
                                <span class="toggle-password show-password" data-toggle="toggle-password"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom-form">
                    <div class="item-button-form">
                        <a href="" class="destroy-form item-button"><?php echo $arrLang['huy']; ?></a>
                    </div>
                    <?php if($flg['doimatkhau'] == 1): ?>
                    <div class="item-button-form">
                        <button class="save-form item-button"><?php echo $arrLang['luu']; ?></button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="tab-pane fade <?php if(Session::has('action') && Session::get('action') == 'change-user'): ?> show active <?php endif; ?>"
    id="content-tab-user">
    <div class="main-form">
        <form method="post" id="form-change-user" action="<?php echo e(route('employer-change-user')); ?>">
            <?php echo csrf_field(); ?>
            <div class="form-change-password">
                <div class="row item-form-change-password">
                    <div class="col-md-4">
                        <label><?php echo $arrLang['hoten']; ?> <span class="text-danger">*</span></label>
                    </div>
                    <div class="col-md-8">
                        <div class="group-item-field field-item-st1 field-password">
                            <div class="item-form">
                                <input class="item-field" type="text" name="name" value="<?php echo e($user->name); ?>" placeholder=""
                                    autocomplete="off">
                                <?php if($errors->has('name')): ?>
                                <label id="current_password-error" class="error"
                                    for="current_password"><?php echo e($errors->first('name')); ?></label>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row item-form-change-password">
                    <div class="col-md-4">
                        <label><?php echo $arrLang['email']; ?> <span class="text-danger">*</span></label>
                    </div>
                    <div class="col-md-8">
                        <div class="group-item-field field-item-st1 field-password">
                            <div class="item-form">
                                <input class="item-field" type="text" name="email" placeholder=""
                                    value="<?php echo e($user->email); ?>" autocomplete="off">
                                <?php if($errors->has('email')): ?>
                                <label id="password-error" class="error"
                                    for="password"><?php echo e($errors->first('email')); ?></label>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row item-form-change-password">
                    <div class="col-md-4">
                        <label><?php echo $arrLang['sodienthoai']; ?> <span class="text-danger">*</span></label>
                    </div>
                    <div class="col-md-8">
                        <div class="group-item-field field-item-st1 field-password">
                            <div class="item-form">
                                <input class="item-field" type="text" name="mobile" value="<?php echo e($user->mobile); ?>"
                                    placeholder="" autocomplete="off">
                                <?php if($errors->has('confirm_password')): ?>
                                <label id="confirm_password-error" class="error"
                                    for="confirm_password"><?php echo e($errors->first('mobile')); ?></label>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row item-form-change-password">
                    <div class="col-md-4">
                        <label><?php echo $arrLang['tencongty']; ?> </label>
                    </div>
                    <div class="col-md-8">
                        <div class="group-item-field field-item-st1 field-password">
                            <div class="item-form">
                                <input disabled class="item-field" type="text" name="company_name"
                                    value="<?php echo e($company->name); ?>" placeholder="" autocomplete="off">
                                <?php if($errors->has('company_name')): ?>
                                <label id="confirm_password-error" class="error"
                                    for="confirm_password"><?php echo e($errors->first('company_name')); ?></label>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom-form">
                    <div class="item-button-form">
                        <a href="" class="destroy-form item-button"><?php echo $arrLang['huy']; ?></a>
                    </div>
                    <?php if($flg['thongtin_ntd'] == 1): ?>
                    <div class="item-button-form">
                        <button class="save-form item-button"><?php echo $arrLang['luu']; ?></button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
</div>
</div>


<div class="modal fade" id="modal-addvideo" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header justify-content-center">
                <?php echo e($arrLang['themvideo']); ?>

            </div>
            <div class="modal-body">
                <div class="modal-form">
                    <div class="item-modal-form">
                        <label><?php echo e($arrLang['idvideo']); ?></label>
                        <input placeholder="Nhập ID video">
                    </div>
                    <p><?php echo e($arrLang['idlachuoikitu']); ?></p>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-cancel" data-bs-dismiss="modal"><?php echo e($arrLang['huy']); ?></button>
                <button type="button" class="btn btn-save btn-add-video"><?php echo e($arrLang['xacnhan']); ?></button>
            </div>
        </div>
    </div>
</div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script src="<?php echo e(asset2('frontend/asset/js/additional-methods.min.js')); ?>"></script>
<script src="<?php echo e(asset2('frontend/asset/ckeditor/ckeditor.js')); ?>"></script>
<script>
    $(document).ready(function () {
            // $("#modal-addvideo").modal('show');
            var editor;
            CKEDITOR.on('instanceReady', function (ev) {
                editor = ev.editor;
                editor.on('readOnly', function () {
                });
            });

            function toggleReadOnly(isReadOnly) {
                editor.setReadOnly(isReadOnly);
            }

            CKEDITOR.replace('editor1');
            $('[data-toggle="edit-form"]').click(function () {
                var field = $(this).parents('.wapper-form').find(':disabled');
                field.each(function (i, v) {
                    $(v).prop("disabled", false);
                });
                CKEDITOR.isReadOnly = false;
                toggleReadOnly(false);
                $('.header-tab').hide();
                $('.header-company-info').show();
                $(this).parents('.wapper-form').find('.bottom-form').show();
                $(this).hide();

                $('.add-item-image').addClass('active');
                $('.add-item-video').addClass('active');
                $('.btn-x').addClass('active');
                $('.btn-y').addClass('active');
                $('.upload-logo').addClass('active');
                $('.upload-banner').addClass('active');
                $('.upload-business-registration').addClass('active');
            });

            $("#form-change-password").validate({
                rules: {
                    current_password: {
                        required: true,
                    },
                    password: {
                        required: true,
                        minlength: 8,
                        maxlength: 20,
                        notEqual: true
                    },
                    confirm_password: {
                        required: true,
                    },
                },
                messages: {
                    current_password: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    password: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        minlength: '<?php echo e(__('frontend/validation.min', ['min' => 8])); ?>',
                        maxlength: '<?php echo e(__('frontend/validation.max', ['max' => 20])); ?>',
                        notEqual: '<?php echo e(__('frontend/validation.not_equal_password')); ?>',
                    },
                    confirm_password: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        equalTo: '<?php echo e(__('frontend/validation.same')); ?>',
                    },
                },
                submitHandler: function (form) {
                    form.submit();
                }
            });

            $("#form-change-user").validate({
                rules: {
                    name: {
                        required: true,
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    mobile: {
                        required: true,
                        minlength: 10,
                        maxlength: 16,
                        regex: true,
                    },
                },
                messages: {
                    name: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    email: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        email: '<?php echo e(__('frontend/validation.email')); ?>'

                    },
                    mobile: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        minlength: '<?php echo e(__('frontend/validation.min', ['min' => 10])); ?>',
                        maxlength: '<?php echo e(__('frontend/validation.max', ['max' => 16])); ?>',
                        regex: '<?php echo e(__('frontend/validation.regex_phone')); ?>',
                    },
                },
                submitHandler: function (form) {
                    form.submit();
                }
            });

            $("#form-company-info").validate({
                ignore: [],
                rules: {
                    name: {
                        required: true,
                    },
                    // website: {
                    //     required: true,
                    // },
                    scale: {
                        required: true,
                    },
                    "address[0][area]": {
                        required: true,
                    },
                    "address[0][address]": {
                        required: true,
                    },
                    about: {
                        required: true,
                    },
                    'career[]': {
                        required: true,
                    },
                    // logo: {
                    //     required: true,
                    // },
                    // banner: {
                    //     required: true,
                    // },
                },
                messages: {
                    name: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    // website: {
                    //     required: '<?php echo e(__('frontend/validation.required')); ?>',
                    // },
                    scale: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    "address[0][area]": {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    "address[0][address]": {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    about: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    'career[]': {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    
                    
                    
                    
                    
                    
                },
                highlight: function (element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        elem.next().parent().addClass(errorClass);
                    } else if (elem.hasClass("file-browserinput")) {
                        elem.parents('.file-browser').find('.file-browser-mask').addClass(errorClass)
                    } else {
                        elem.addClass(errorClass);
                    }
                },
                unhighlight: function (element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        $("#select2-" + elem.attr("id") + "-container").parent().removeClass(errorClass);
                    } else if (elem.hasClass("file-browserinput")) {
                        elem.parents('.file-browser').find('.file-browser-mask').removeClass(errorClass)
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function (error, element) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        element = elem.next();
                        error.insertAfter(element);
                    } else if (elem.hasClass("file-browserinput")) {
                        element = elem.parents('.file-browser').find('.file-browser-mask');
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function (form) {
                    form.submit();
                }
            });

            $.validator.addMethod('regex', function (value) {
                var regex = /^[0-9()+.-]*$/;
                return value.trim().match(regex);
            });

            jQuery.validator.addMethod("notEqual", function (value, element, param) {
                let current_pass = $('#current_password').val();
                return this.optional(element) || value != current_pass;
            });

            $.validator.addMethod('filesize', function (value, element, param) {
                return this.optional(element) || (element.files[0].size <= param * 1000000)
            }, 'File size must be less than {0} MB');

            $(document).on('click', '.upload-logo.active', function () {
                $("input[name='logo']").trigger('click');
            });

            $("input[name='logo']").change(function () {
                readURL(this, 'upload-logo');
            });

            $(document).on('click', '.upload-banner.active', function () {
                $("input[name='banner']").trigger('click');
            });


            $("input[name='banner']").change(function () {
                readURL(this, 'upload-banner');
            });

            $(document).on('click', '.upload-business-registration.active', function () {
                $("input[name='business_registration_number']").trigger('click');
            });

            $("input[name='business_registration_number']").change(function () {
                readURL(this, 'upload-business-registration');
            });

            $(document).on('click', '.add-item-image.active', function () {
                let index = $(this).data('index');
                let length = $('.add-item-image').length;
                let parent = $(this).parent().parent();
                let html = `
                    <div class="frame">
                        <div class="col-gallery add-item-gallery add-item-image item-gallery-${length} active" data-index="${length}"></div>
                        <input type="file" name="image[${length}]" hidden>
                    </div>
                `;
                if (length <= 5 && index == (length - 1)) {
                    $(parent).append(html);
                }

                $('input[name="image[' + index + ']"]').rules("add", {
                    required: true,
                    extension: "jpeg,jpg,png",
                    filesize: 0.5, // <- 500 kb
                    messages: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        extension: '<?php echo e(__('frontend/validation.format_image')); ?>',
                        filesize: '<?php echo e(__('frontend/validation.image_max')); ?>',
                    }
                });

                $("input[name='image[" + (index) + "]']").trigger('click');
            });

            for (let i = 0; i <= 5; i++) {
                $(document).on('change', "input[name='image[" + i + "]']", function () {
                    readURL(this, 'item-gallery-' + i);
                });
            }

            function readURL(input, classParent) {
                $('.' + classParent).html(`<img id="${classParent}" src="#" alt="your image"/>`);
                if (input.files && input.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        $('#' + classParent).attr('src', e.target.result).css('width', '100%')
                    };
                    reader.readAsDataURL(input.files[0]);
                }
            }

            $(document).on('click', '.add-item-video.active', function () {
                let index = $(this).data('index');
                $('.item-modal-form').find('input').attr('name', 'id-video-' + index);
                $('.btn-add-video').attr('data-index', index);
                $("#modal-addvideo").modal('show');
            });

            $(document).on('click', '.btn-add-video', function () {
                let index = $(this).attr("data-index");
                let length = $('.add-item-video').length;
                let parent = $('.item-video-' + index).parent().parent();
                let value = $("input[name='id-video-" + index + "']").val();
                let html = `
                    <div class="frame">
                    <div class="col-gallery add-item-gallery add-item-video  item-video-${length} active" data-index="${length}">
                    </div>
                    <input type="text" name="video[${length}]" hidden>
                    </div>
                `;
                if (length <= 5 && index == (length - 1)) {
                    $(parent).append(html);
                }
                $('.item-video-' + index).html(`
                    <iframe width="158" height="88" src="https://www.youtube.com/embed/${value}"></iframe>
                `);
                $("input[name='video[" + index + "]']").val(value);
                $("#modal-addvideo").modal('hide');
            });

            $(document).on('click', '.btn-x.active', function () {
                let parent = $(this).parent();
                let child = $(parent).children();
                let index = $(child[0]).data('index');
                $(child[0]).children().remove();
                $("input[name='image[" + index + "]']").prop('type', 'file');

                $('input[name="image[' + index + ']"]').rules("add", {
                    // required: true,
                    extension: "jpeg,jpg,png",
                    filesize: 0.5, // <- 500 kb
                    messages: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        extension: '<?php echo e(__('frontend/validation.format_image')); ?>',
                        filesize: '<?php echo e(__('frontend/validation.image_max')); ?>',
                    }
                });
            });

            $(document).on('click', '.btn-y.active', function () {
                let parent = $(this).parent();
                let child = $(parent).children();
                let index = $(child[0]).data('index');
                $(child[0]).children().remove();
                $("input[name='video[" + index + "]']").val('');
            });

            $('#career-select').select2({
                allowClear: true,
                maximumSelectionLength: 3
            });
        });
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('frontend.layouts.employer.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Projects\HRI\RecLand\resources\views/frontend/pages/employer/company_profile.blade.php ENDPATH**/ ?>
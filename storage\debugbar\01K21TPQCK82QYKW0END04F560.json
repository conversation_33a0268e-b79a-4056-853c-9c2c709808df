{"__meta": {"id": "01K21TPQCK82QYKW0END04F560", "datetime": "2025-08-07 15:35:02", "utime": **********.67662, "method": "GET", "uri": "/employer/company-profile", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 33, "messages": [{"message": "[15:35:02] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.049943, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.050295, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-company-profile' limit 1\\n-- \",\n    \"Time:\": 27.87\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.110238, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local\\/employer\\/company-profile', 'http:\\/\\/recland.local\\/employer\\/dashboard', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36 Edg\\\\\\/*********\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\\\\/dashboard\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndsQnRwR1JuTVo0NHQzbW5HQTFEOVE9PSIsInZhbHVlIjoidDdlZFJzUjdLMStNYWtiQVVWR0xQWUExRndlOE5NUjNjTXRucCtWbXJaUU1wQnp5VTRRUTlkRXErUnplWENnZGVtL3ZoSkg4TUFtV3FBTVJKMTlPNVV1UDVsbHFSZGdGZW03VHVWV2MzOUlEUTJUMlhPS0lIeHppYUNrY3JWdmwiLCJtYWMiOiJlOGFjY2U0Njc1N2M5YjIyNzgyOTY2MTE1NmRhM2UxYWEyZDgzY2QzMGEzNzk1Zjg4MGZmYmJlYTA2NDg3MWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImR4MGRIWVdVTG03Z1ZDRVlXc3k5OGc9PSIsInZhbHVlIjoiZ21RNjhVZ2JMc0pONXMweVlRbDRHVXA0SlVkeGM3dWR6REhhRUE0bzE5ZmlPV1BhaThHRi82Tm5JbWlRczloa2lxQ0RESE1ob2VubklBV0ZFVHU5RmF2L1c5eG5mWDBSQnhCak1wRUgzZ0VZa2pRcjdLWmY1N0pSeTJCaWE3TGIiLCJtYWMiOiIwMWJkOTYzMWZhNmYzN2FlNjI5YWQyZTllOWMzMDk4NWMwZmQ2MTkwMmFmOTY3ZTI0MjhkMjAxZGU0N2FhNTkxIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:35:02', '2025-08-07 15:35:02')\\n-- \",\n    \"Time:\": 0.61\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.149162, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '7490' limit 1\\n-- \",\n    \"Time:\": 0.64\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.155627, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.77\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.160842, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.162987, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7490' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 5.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.169918, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-company-profile' limit 1\\n-- \",\n    \"Time:\": 0.46\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.171806, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.173997, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.24\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.175346, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '698' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.17731, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\\Projects\\HRI\\RecLand\\storage\\framework\\views\\782271535f7e1d3387af50921469581910a76c61.php on line 99", "message_html": null, "is_string": false, "label": "warning", "time": **********.604866, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\Company.php on line 174", "message_html": null, "is_string": false, "label": "warning", "time": **********.62173, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\Company.php on line 197", "message_html": null, "is_string": false, "label": "warning", "time": **********.622046, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 1.09\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.629073, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.83\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.631671, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.86\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.633751, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.635695, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.81\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.637863, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.641066, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.642951, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `meta_data` where `meta_data`.`object_type` = 'App\\\\Models\\\\User' and `meta_data`.`object_id` = '7490' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1\\n-- \",\n    \"Time:\": 0.75\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.646578, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.65\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.652231, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '7490' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.59\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.654088, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.656715, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.659809, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.661357, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.662933, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.66446, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.666059, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.667619, "xdebug_link": null, "collector": "log"}, {"message": "[15:35:02] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (7490)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.66916, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754555701.656064, "end": **********.676677, "duration": 1.0206129550933838, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1754555701.656064, "relative_start": 0, "end": **********.029334, "relative_end": **********.029334, "duration": 0.*****************, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.029346, "relative_start": 0.*****************, "end": **********.676679, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "647ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.041137, "relative_start": 0.****************, "end": **********.046347, "relative_end": **********.046347, "duration": 0.005209922790527344, "duration_str": "5.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "1x frontend.pages.employer.company_profile", "param_count": null, "params": [], "start": **********.189704, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/company_profile.blade.phpfrontend.pages.employer.company_profile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Femployer%2Fcompany_profile.blade.php&line=1", "ajax": false, "filename": "company_profile.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.employer.company_profile"}, {"name": "1x frontend.layouts.employer.app", "param_count": null, "params": [], "start": **********.625636, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/app.blade.phpfrontend.layouts.employer.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.employer.app"}, {"name": "2x frontend.layouts.user.avatar", "param_count": null, "params": [], "start": **********.626329, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/user/avatar.blade.phpfrontend.layouts.user.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fuser%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.user.avatar"}, {"name": "2x frontend.layouts.employer.menu", "param_count": null, "params": [], "start": **********.626834, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.phpfrontend.layouts.employer.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.employer.menu"}, {"name": "1x frontend.layouts.modal.employer_confirm", "param_count": null, "params": [], "start": **********.644283, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.phpfrontend.layouts.modal.employer_confirm", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmodal%2Femployer_confirm.blade.php&line=1", "ajax": false, "filename": "employer_confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.modal.employer_confirm"}, {"name": "1x frontend.layouts.login.app", "param_count": null, "params": [], "start": **********.647752, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/login/app.blade.phpfrontend.layouts.login.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Flogin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.login.app"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": **********.64863, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.login.header", "param_count": null, "params": [], "start": **********.649162, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.phpfrontend.inc_layouts.login.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.header"}, {"name": "2x frontend.inc_layouts.notification.drop-notification", "param_count": null, "params": [], "start": **********.655126, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/notification/drop-notification.blade.phpfrontend.inc_layouts.notification.drop-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fnotification%2Fdrop-notification.blade.php&line=1", "ajax": false, "filename": "drop-notification.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.inc_layouts.notification.drop-notification"}, {"name": "1x frontend.inc_layouts.login.footer_v2", "param_count": null, "params": [], "start": **********.670646, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/footer_v2.blade.phpfrontend.inc_layouts.login.footer_v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Ffooter_v2.blade.php&line=1", "ajax": false, "filename": "footer_v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.footer_v2"}, {"name": "1x frontend.inc_layouts.login.modal_report", "param_count": null, "params": [], "start": **********.671811, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/modal_report.blade.phpfrontend.inc_layouts.login.modal_report", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fmodal_report.blade.php&line=1", "ajax": false, "filename": "modal_report.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.modal_report"}, {"name": "1x frontend.partials.bug_report_modal", "param_count": null, "params": [], "start": **********.672614, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/partials/bug_report_modal.blade.phpfrontend.partials.bug_report_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fbug_report_modal.blade.php&line=1", "ajax": false, "filename": "bug_report_modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.partials.bug_report_modal"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.673919, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET employer/company-profile", "middleware": "web, localization, visit-website, check-employer, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@companyProfile<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=351\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "as": "employer-company-profile", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=351\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:351-389</a>"}, "queries": {"count": 28, "nb_statements": 28, "nb_visible_statements": 28, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.048539999999999986, "accumulated_duration_str": "48.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-company-profile' limit 1", "type": "query", "params": [], "bindings": ["employer-company-profile"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.082622, "duration": 0.027870000000000002, "duration_str": "27.87ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "a2fb194f71247ecbe73294b393cbedbd845542b0d8c7d55f7c5b9cba51e1c1cf"}, "start_percent": 0, "width_percent": 57.417}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/company-profile', 'http://recland.local/employer/dashboard', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\/dashboard\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndsQnRwR1JuTVo0NHQzbW5HQTFEOVE9PSIsInZhbHVlIjoidDdlZFJzUjdLMStNYWtiQVVWR0xQWUExRndlOE5NUjNjTXRucCtWbXJaUU1wQnp5VTRRUTlkRXErUnplWENnZGVtL3ZoSkg4TUFtV3FBTVJKMTlPNVV1UDVsbHFSZGdGZW03VHVWV2MzOUlEUTJUMlhPS0lIeHppYUNrY3JWdmwiLCJtYWMiOiJlOGFjY2U0Njc1N2M5YjIyNzgyOTY2MTE1NmRhM2UxYWEyZDgzY2QzMGEzNzk1Zjg4MGZmYmJlYTA2NDg3MWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImR4MGRIWVdVTG03Z1ZDRVlXc3k5OGc9PSIsInZhbHVlIjoiZ21RNjhVZ2JMc0pONXMweVlRbDRHVXA0SlVkeGM3dWR6REhhRUE0bzE5ZmlPV1BhaThHRi82Tm5JbWlRczloa2lxQ0RESE1ob2VubklBV0ZFVHU5RmF2L1c5eG5mWDBSQnhCak1wRUgzZ0VZa2pRcjdLWmY1N0pSeTJCaWE3TGIiLCJtYWMiOiIwMWJkOTYzMWZhNmYzN2FlNjI5YWQyZTllOWMzMDk4NWMwZmQ2MTkwMmFmOTY3ZTI0MjhkMjAxZGU0N2FhNTkxIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-07 15:35:02', '2025-08-07 15:35:02')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local/employer/company-profile", "http://recland.local/employer/dashboard", "[\"en-us\",\"en\",\"vi\",\"nl\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/dashboard\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndsQnRwR1JuTVo0NHQzbW5HQTFEOVE9PSIsInZhbHVlIjoidDdlZFJzUjdLMStNYWtiQVVWR0xQWUExRndlOE5NUjNjTXRucCtWbXJaUU1wQnp5VTRRUTlkRXErUnplWENnZGVtL3ZoSkg4TUFtV3FBTVJKMTlPNVV1UDVsbHFSZGdGZW03VHVWV2MzOUlEUTJUMlhPS0lIeHppYUNrY3JWdmwiLCJtYWMiOiJlOGFjY2U0Njc1N2M5YjIyNzgyOTY2MTE1NmRhM2UxYWEyZDgzY2QzMGEzNzk1Zjg4MGZmYmJlYTA2NDg3MWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImR4MGRIWVdVTG03Z1ZDRVlXc3k5OGc9PSIsInZhbHVlIjoiZ21RNjhVZ2JMc0pONXMweVlRbDRHVXA0SlVkeGM3dWR6REhhRUE0bzE5ZmlPV1BhaThHRi82Tm5JbWlRczloa2lxQ0RESE1ob2VubklBV0ZFVHU5RmF2L1c5eG5mWDBSQnhCak1wRUgzZ0VZa2pRcjdLWmY1N0pSeTJCaWE3TGIiLCJtYWMiOiIwMWJkOTYzMWZhNmYzN2FlNjI5YWQyZTllOWMzMDk4NWMwZmQ2MTkwMmFmOTY3ZTI0MjhkMjAxZGU0N2FhNTkxIiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Edge", "127.0.0.1", null, null, "2025-08-07 15:35:02", "2025-08-07 15:35:02"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.148628, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 57.417, "width_percent": 1.257}, {"sql": "select * from `users` where `id` = 7490 limit 1", "type": "query", "params": [], "bindings": [7490], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.1550531, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "46fb2a7e2da69ce8dd6b76eee0f083f3fd38c5f3e291e80b6e2a5dd1df6426fd"}, "start_percent": 58.673, "width_percent": 1.319}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 21, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.1601398, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 59.992, "width_percent": 1.586}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 26, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 28, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.162725, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 61.578, "width_percent": 0.68}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7490 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7490], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 22, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}], "start": **********.164645, "duration": 0.00534, "duration_str": "5.34ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:125", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=125", "ajax": false, "filename": "EmployerController.php", "line": "125"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "2acb24b13514507d20205de68fbb9c1fa196748160210213f26fc2003be4b8af"}, "start_percent": 62.258, "width_percent": 11.001}, {"sql": "select * from `seos` where `key` = 'employer-company-profile' limit 1", "type": "query", "params": [], "bindings": ["employer-company-profile"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 358}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.171411, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "a2fb194f71247ecbe73294b393cbedbd845542b0d8c7d55f7c5b9cba51e1c1cf"}, "start_percent": 73.259, "width_percent": 0.948}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 359}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.173302, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EmployerController.php:359", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=359", "ajax": false, "filename": "EmployerController.php", "line": "359"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 74.207, "width_percent": 1.566}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 359}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.175169, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "EmployerController.php:359", "source": {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=359", "ajax": false, "filename": "EmployerController.php", "line": "359"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 75.773, "width_percent": 0.494}, {"sql": "select * from `companies` where `companies`.`id` = 698 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [698], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 383}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.176986, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "28b43cdb8c1845561ccb8d3e6e53092de39c0fef5a624fe2a1970a69f2fb6c84"}, "start_percent": 76.267, "width_percent": 0.803}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.628056, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 77.07, "width_percent": 2.246}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.630911, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 79.316, "width_percent": 1.71}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.632957, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 81.026, "width_percent": 1.772}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.634997, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 82.798, "width_percent": 1.566}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6372042, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 84.363, "width_percent": 1.669}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.640372, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 86.032, "width_percent": 1.566}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6422849, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 87.598, "width_percent": 1.504}, {"sql": "select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = 7490 and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 7490, "employer_confirmed_at"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, {"index": 19, "namespace": "view", "name": "frontend.layouts.modal.employer_confirm", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.php", "line": 5}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.645896, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "User.php:197", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=197", "ajax": false, "filename": "User.php", "line": "197"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `meta_data` where `meta_data`.`object_type` = ? and `meta_data`.`object_id` = ? and `meta_data`.`object_id` is not null and `key` = ? limit 1", "hash": "c1027118294dd0a667dd167a5d2360e5e721332b18f22b6ea04e67176508a422"}, "start_percent": 89.102, "width_percent": 1.545}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 7490 and `notifications`.`notifiable_id` is not null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 7490], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.651648, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:98", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=98", "ajax": false, "filename": "header.blade.php", "line": "98"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null order by `created_at` desc", "hash": "e4fea375135a93679975c14bd461e5b822cc3df34bc0e70d04f9e664a10a8c6a"}, "start_percent": 90.647, "width_percent": 1.339}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 7490 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 7490], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.653563, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:99", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=99", "ajax": false, "filename": "header.blade.php", "line": "99"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "hash": "e363911ffc4721973d884b5a040403f6ef56d5e8d48ec406063838d6a6f29342"}, "start_percent": 91.986, "width_percent": 1.215}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6563509, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:243", "source": {"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=243", "ajax": false, "filename": "header.blade.php", "line": "243"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 93.201, "width_percent": 0.886}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6594648, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 94.087, "width_percent": 0.845}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.66102, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 94.932, "width_percent": 0.824}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6625621, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 95.756, "width_percent": 0.906}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6641269, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 96.663, "width_percent": 0.845}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6657128, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 97.507, "width_percent": 0.845}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6672828, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 98.352, "width_percent": 0.824}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.668823, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (7490)", "hash": "55584bbb82d04cbe2cd96dac153f39be3320806c2dfe2df6f448e1dccb964380"}, "start_percent": 99.176, "width_percent": 0.824}]}, "models": {"data": {"App\\Models\\EmployerType": {"retrieved": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmployerType.php&line=1", "ajax": false, "filename": "EmployerType.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\MetaData": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FMetaData.php&line=1", "ajax": false, "filename": "MetaData.php", "line": "?"}}}, "count": 24, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 23, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7490", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/employer/company-profile", "action_name": "employer-company-profile", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@companyProfile", "uri": "GET employer/company-profile", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@companyProfile<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=351\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=351\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:351-389</a>", "middleware": "web, localization, visit-website, check-employer", "duration": "1.02s", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1795756689 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1795756689\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1314856846 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1314856846\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1412866196 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://recland.local/employer/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndsQnRwR1JuTVo0NHQzbW5HQTFEOVE9PSIsInZhbHVlIjoidDdlZFJzUjdLMStNYWtiQVVWR0xQWUExRndlOE5NUjNjTXRucCtWbXJaUU1wQnp5VTRRUTlkRXErUnplWENnZGVtL3ZoSkg4TUFtV3FBTVJKMTlPNVV1UDVsbHFSZGdGZW03VHVWV2MzOUlEUTJUMlhPS0lIeHppYUNrY3JWdmwiLCJtYWMiOiJlOGFjY2U0Njc1N2M5YjIyNzgyOTY2MTE1NmRhM2UxYWEyZDgzY2QzMGEzNzk1Zjg4MGZmYmJlYTA2NDg3MWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImR4MGRIWVdVTG03Z1ZDRVlXc3k5OGc9PSIsInZhbHVlIjoiZ21RNjhVZ2JMc0pONXMweVlRbDRHVXA0SlVkeGM3dWR6REhhRUE0bzE5ZmlPV1BhaThHRi82Tm5JbWlRczloa2lxQ0RESE1ob2VubklBV0ZFVHU5RmF2L1c5eG5mWDBSQnhCak1wRUgzZ0VZa2pRcjdLWmY1N0pSeTJCaWE3TGIiLCJtYWMiOiIwMWJkOTYzMWZhNmYzN2FlNjI5YWQyZTllOWMzMDk4NWMwZmQ2MTkwMmFmOTY3ZTI0MjhkMjAxZGU0N2FhNTkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412866196\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-762043869 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">J9m30nlyDaWRKw7nMx9mDA5J2JdNAotbwYLo7eQ1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762043869\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-764812033 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 08:35:02 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764812033\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1901725565 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BIpyvKOta3v6guJ9ZBVUAfvDU1tRi2i9rayXqgmb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://recland.local/employer/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7490</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901725565\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/employer/company-profile", "action_name": "employer-company-profile", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@companyProfile"}, "badge": null}}